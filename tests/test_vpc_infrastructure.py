#!/usr/bin/env python3
"""
Test suite for GCP VPC infrastructure validation.
Tests the deployed VPC configuration to ensure it matches the expected state.
"""

import json
import subprocess
import sys
import pytest
from typing import Dict, List, Any


class VPCInfrastructureTests:
    """Test class for VPC infrastructure validation."""
    
    def __init__(self):
        self.project_id = "p-dev-bt-concept-ywqb-1"
        self.vpc_name = "main-vpc"
        self.region = "us-central1"
        self.expected_subnets = {
            "web-subnet": {
                "cidr": "10.0.1.0/24",
                "private_google_access": False,
                "region": "us-central1"
            },
            "app-subnet": {
                "cidr": "10.0.2.0/24", 
                "private_google_access": True,
                "region": "us-central1"
            },
            "db-subnet": {
                "cidr": "********/24",
                "private_google_access": True,
                "region": "us-central1"
            },
            "gke-subnet": {
                "cidr": "********/24",
                "private_google_access": True,
                "region": "us-central1",
                "secondary_ranges": [
                    {"range_name": "gke-pods", "ip_cidr_range": "********/16"},
                    {"range_name": "gke-services", "ip_cidr_range": "********/16"}
                ]
            }
        }
        self.expected_firewall_rules = [
            "main-vpc-allow-internal",
            "main-vpc-allow-ssh", 
            "main-vpc-allow-http-https",
            "allow-app-to-db",
            "allow-load-balancer"
        ]

    def run_gcloud_command(self, command: List[str]) -> Dict[str, Any]:
        """Run a gcloud command and return JSON output."""
        try:
            result = subprocess.run(
                command + ["--format=json"],
                capture_output=True,
                text=True,
                check=True
            )
            return json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Command failed: {' '.join(command)}")
            print(f"Error: {e.stderr}")
            raise
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON output from: {' '.join(command)}")
            print(f"Output: {result.stdout}")
            raise

    def test_vpc_network_exists(self):
        """Test that the VPC network exists with correct configuration."""
        print("Testing VPC network existence and configuration...")
        
        networks = self.run_gcloud_command([
            "gcloud", "compute", "networks", "list",
            f"--filter=name={self.vpc_name}",
            f"--project={self.project_id}"
        ])
        
        assert len(networks) == 1, f"Expected 1 VPC network, found {len(networks)}"
        
        network = networks[0]
        assert network["name"] == self.vpc_name
        assert network["autoCreateSubnetworks"] == False
        assert network["routingConfig"]["routingMode"] == "REGIONAL"
        
        print("✓ VPC network exists with correct configuration")

    def test_subnets_configuration(self):
        """Test that all subnets exist with correct configuration."""
        print("Testing subnet configuration...")
        
        subnets = self.run_gcloud_command([
            "gcloud", "compute", "networks", "subnets", "list",
            f"--filter=network:projects/{self.project_id}/global/networks/{self.vpc_name}",
            f"--project={self.project_id}"
        ])
        
        subnet_names = [subnet["name"] for subnet in subnets]
        expected_names = list(self.expected_subnets.keys())
        
        assert len(subnets) == len(expected_names), f"Expected {len(expected_names)} subnets, found {len(subnets)}"
        
        for expected_name in expected_names:
            assert expected_name in subnet_names, f"Subnet {expected_name} not found"
        
        # Validate each subnet configuration
        for subnet in subnets:
            subnet_name = subnet["name"]
            expected = self.expected_subnets[subnet_name]
            
            assert subnet["ipCidrRange"] == expected["cidr"], \
                f"Subnet {subnet_name} CIDR mismatch: expected {expected['cidr']}, got {subnet['ipCidrRange']}"
            
            assert subnet["region"].endswith(expected["region"]), \
                f"Subnet {subnet_name} region mismatch"
            
            assert subnet["privateIpGoogleAccess"] == expected["private_google_access"], \
                f"Subnet {subnet_name} private Google access mismatch"
            
            # Check secondary ranges for GKE subnet
            if subnet_name == "gke-subnet":
                secondary_ranges = subnet.get("secondaryIpRanges", [])
                expected_ranges = expected["secondary_ranges"]
                
                assert len(secondary_ranges) == len(expected_ranges), \
                    f"GKE subnet secondary ranges count mismatch"
                
                for expected_range in expected_ranges:
                    found = False
                    for actual_range in secondary_ranges:
                        if (actual_range["rangeName"] == expected_range["range_name"] and
                            actual_range["ipCidrRange"] == expected_range["ip_cidr_range"]):
                            found = True
                            break
                    assert found, f"Secondary range {expected_range} not found in GKE subnet"
        
        print("✓ All subnets configured correctly")

    def test_firewall_rules(self):
        """Test that all expected firewall rules exist."""
        print("Testing firewall rules...")
        
        firewall_rules = self.run_gcloud_command([
            "gcloud", "compute", "firewall-rules", "list",
            f"--filter=network:projects/{self.project_id}/global/networks/{self.vpc_name}",
            f"--project={self.project_id}"
        ])
        
        rule_names = [rule["name"] for rule in firewall_rules]
        
        for expected_rule in self.expected_firewall_rules:
            assert expected_rule in rule_names, f"Firewall rule {expected_rule} not found"
        
        # Test specific firewall rule configurations
        for rule in firewall_rules:
            rule_name = rule["name"]
            
            if rule_name == "main-vpc-allow-ssh":
                assert rule["allowed"][0]["ports"] == ["22"]
                assert "ssh-allowed" in rule["targetTags"]
                
            elif rule_name == "main-vpc-allow-http-https":
                ports = rule["allowed"][0]["ports"]
                assert "80" in ports and "443" in ports
                assert "http-server" in rule["targetTags"]
                assert "https-server" in rule["targetTags"]
                
            elif rule_name == "allow-app-to-db":
                ports = rule["allowed"][0]["ports"]
                assert "3306" in ports and "5432" in ports
                assert "10.0.2.0/24" in rule["sourceRanges"]
                assert "database" in rule["targetTags"]
        
        print("✓ All firewall rules configured correctly")

    def test_cloud_router_and_nat(self):
        """Test that Cloud Router and NAT gateway exist."""
        print("Testing Cloud Router and NAT gateway...")
        
        # Test Cloud Router
        routers = self.run_gcloud_command([
            "gcloud", "compute", "routers", "list",
            f"--filter=network:projects/{self.project_id}/global/networks/{self.vpc_name}",
            f"--project={self.project_id}",
            f"--regions={self.region}"
        ])
        
        assert len(routers) == 1, f"Expected 1 router, found {len(routers)}"
        router = routers[0]
        assert router["name"] == f"{self.vpc_name}-router"
        assert router["bgp"]["asn"] == 64514
        
        # Test NAT gateway
        nats = self.run_gcloud_command([
            "gcloud", "compute", "routers", "nats", "list",
            f"--router={self.vpc_name}-router",
            f"--router-region={self.region}",
            f"--project={self.project_id}"
        ])
        
        assert len(nats) == 1, f"Expected 1 NAT gateway, found {len(nats)}"
        nat = nats[0]
        assert nat["name"] == f"{self.vpc_name}-nat"
        assert nat["natIpAllocateOption"] == "AUTO_ONLY"
        
        print("✓ Cloud Router and NAT gateway configured correctly")

    def test_terraform_outputs(self):
        """Test that Terraform outputs match expected values."""
        print("Testing Terraform outputs...")
        
        try:
            result = subprocess.run(
                ["terraform", "output", "-json"],
                capture_output=True,
                text=True,
                check=True,
                cwd="/Users/<USER>/Documents/augment-projects/gcp-testing"
            )
            outputs = json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Failed to get Terraform outputs: {e.stderr}")
            raise
        
        # Test VPC network outputs
        assert outputs["vpc_network_name"]["value"] == self.vpc_name
        assert self.project_id in outputs["vpc_network_id"]["value"]
        
        # Test subnet outputs
        subnet_ids = outputs["subnet_ids"]["value"]
        subnet_cidrs = outputs["subnet_cidr_ranges"]["value"]
        
        for subnet_name, expected_config in self.expected_subnets.items():
            assert subnet_name in subnet_ids, f"Subnet {subnet_name} not in Terraform outputs"
            assert subnet_cidrs[subnet_name] == expected_config["cidr"]
        
        # Test firewall rule outputs
        default_fw_rules = outputs["default_firewall_rule_ids"]["value"]
        custom_fw_rules = outputs["firewall_rule_ids"]["value"]
        
        assert "allow_internal" in default_fw_rules
        assert "allow_ssh" in default_fw_rules
        assert "allow_http_https" in default_fw_rules
        assert "allow-app-to-db" in custom_fw_rules
        assert "allow-load-balancer" in custom_fw_rules
        
        print("✓ Terraform outputs are correct")

    def run_all_tests(self):
        """Run all infrastructure tests."""
        print("Starting VPC infrastructure tests...\n")
        
        try:
            self.test_vpc_network_exists()
            self.test_subnets_configuration()
            self.test_firewall_rules()
            self.test_cloud_router_and_nat()
            self.test_terraform_outputs()
            
            print("\n🎉 All tests passed! VPC infrastructure is correctly configured.")
            return True
            
        except Exception as e:
            print(f"\n❌ Test failed: {str(e)}")
            return False


if __name__ == "__main__":
    tester = VPCInfrastructureTests()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
