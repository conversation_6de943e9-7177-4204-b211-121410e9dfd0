# VPC Infrastructure Tests

This directory contains comprehensive tests to validate the GCP VPC infrastructure deployment and ensure it's working as defined.

## Test Types

### 1. Infrastructure Tests (`test_vpc_infrastructure.py`)
Validates that the deployed infrastructure matches the expected configuration:
- ✅ VPC network exists with correct settings
- ✅ All subnets are created with proper CIDR ranges
- ✅ Private Google access is configured correctly
- ✅ Secondary IP ranges for GKE are set up
- ✅ Firewall rules are created and configured properly
- ✅ Cloud Router and NAT gateway are deployed
- ✅ Flow logs are enabled on subnets
- ✅ Terraform outputs match expected values

### 2. Terraform Validation Tests (`test_terraform_validation.py`)
Validates the Terraform configuration and state:
- ✅ Terraform configuration is valid
- ✅ Files are properly formatted
- ✅ No infrastructure drift detected
- ✅ State consistency checks
- ✅ All expected resources in state
- ✅ Outputs are accessible and correct
- ✅ Provider configuration is valid

### 3. Connectivity Tests (`test_connectivity.py`)
Tests actual network connectivity and firewall functionality:
- ✅ Creates test VMs in different subnets
- ✅ Tests SSH connectivity to VMs
- ✅ Validates internal communication between subnets
- ✅ Tests firewall rules (SSH, HTTP/HTTPS, database ports)
- ✅ Validates NAT gateway functionality for private VMs
- ✅ Tests internet connectivity from private subnets
- ✅ Automatically cleans up test resources

**⚠️ Note**: Connectivity tests create and delete VMs, which may incur small charges.

## Running Tests

### Prerequisites
```bash
# Install Python dependencies
make install-test-deps
# or manually:
pip3 install -r tests/requirements.txt
```

### Run All Tests
```bash
make test
```

### Run Specific Test Types
```bash
# Infrastructure validation only
make test-infra

# Terraform validation only  
make test-terraform

# Connectivity tests only (creates VMs)
make test-connectivity

# All tests except connectivity (no VM creation)
make test-safe
```

### Manual Test Execution
```bash
cd tests

# Run all tests
python3 run_all_tests.py

# Run specific test types
python3 run_all_tests.py --test-type infrastructure
python3 run_all_tests.py --test-type terraform
python3 run_all_tests.py --test-type connectivity

# Skip connectivity tests
python3 run_all_tests.py --skip-connectivity

# Run individual test files
python3 test_vpc_infrastructure.py
python3 test_terraform_validation.py
python3 test_connectivity.py
```

## Test Configuration

The tests are configured for the current deployment:
- **Project ID**: `p-dev-bt-concept-ywqb-1`
- **VPC Name**: `main-vpc`
- **Region**: `us-central1`
- **Zone**: `us-central1-a`

To use with a different project or configuration, update the variables in each test file.

## Expected Test Results

### Infrastructure Tests
- Validates 12 deployed resources
- Checks 4 subnets with correct CIDR ranges
- Verifies 5 firewall rules
- Confirms Cloud Router and NAT gateway

### Terraform Tests
- Validates configuration syntax
- Checks for infrastructure drift
- Verifies state consistency
- Confirms all outputs are accessible

### Connectivity Tests
- Creates 3 test VMs (web, app, database tiers)
- Tests SSH access to public VM
- Validates internal communication
- Tests database port access (3306, 5432)
- Confirms NAT gateway internet access
- Automatically cleans up all test resources

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   gcloud auth application-default login
   ```

2. **Missing Dependencies**
   ```bash
   make install-test-deps
   ```

3. **Terraform State Issues**
   ```bash
   terraform refresh
   ```

4. **VM Creation Failures**
   - Check quotas in GCP console
   - Verify subnet configurations
   - Ensure firewall rules allow SSH

### Test Failures

If tests fail:
1. Check the detailed error output
2. Verify GCP authentication
3. Confirm Terraform state is current
4. Check GCP quotas and permissions
5. Review firewall rules and network configuration

## Continuous Integration

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run VPC Tests
  run: |
    make install-test-deps
    make test-safe  # Skip connectivity tests in CI
```

## Test Coverage

The test suite provides comprehensive coverage:
- **Configuration Validation**: Terraform syntax and formatting
- **Infrastructure Validation**: Deployed resources match expectations  
- **State Management**: No drift, consistent state
- **Network Functionality**: Actual connectivity and firewall testing
- **Security**: Firewall rules work as intended
- **Internet Access**: NAT gateway functionality

This ensures your VPC infrastructure is not only deployed correctly but also functioning as designed.
