#!/usr/bin/env python3
"""
Connectivity tests for GCP VPC infrastructure.
Tests network connectivity and firewall rules by creating test VMs.
"""

import json
import subprocess
import time
import sys
from typing import Dict, List


class ConnectivityTests:
    """Test class for VPC connectivity validation."""
    
    def __init__(self):
        self.project_id = "p-dev-bt-concept-ywqb-1"
        self.vpc_name = "main-vpc"
        self.region = "us-central1"
        self.zone = "us-central1-a"
        self.test_vms = {
            "web-test-vm": {
                "subnet": "web-subnet",
                "tags": ["ssh-allowed", "http-server"],
                "external_ip": True
            },
            "app-test-vm": {
                "subnet": "app-subnet", 
                "tags": ["ssh-allowed"],
                "external_ip": False
            },
            "db-test-vm": {
                "subnet": "db-subnet",
                "tags": ["ssh-allowed", "database"],
                "external_ip": False
            }
        }

    def run_command(self, command: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=check
            )
            return result
        except subprocess.CalledProcessError as e:
            print(f"Command failed: {' '.join(command)}")
            print(f"Error: {e.stderr}")
            if check:
                raise
            return e

    def create_test_vm(self, vm_name: str, config: Dict) -> bool:
        """Create a test VM for connectivity testing."""
        print(f"Creating test VM: {vm_name}")
        
        command = [
            "gcloud", "compute", "instances", "create", vm_name,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            f"--subnet=projects/{self.project_id}/regions/{self.region}/subnetworks/{config['subnet']}",
            "--machine-type=e2-micro",
            "--image-family=debian-11",
            "--image-project=debian-cloud",
            "--boot-disk-size=10GB",
            "--boot-disk-type=pd-standard",
            "--metadata=startup-script=#!/bin/bash\napt-get update\napt-get install -y netcat-openbsd curl",
            "--quiet"
        ]
        
        if config["tags"]:
            command.extend(["--tags", ",".join(config["tags"])])
        
        if not config["external_ip"]:
            command.append("--no-address")
        
        result = self.run_command(command, check=False)
        
        if result.returncode == 0:
            print(f"✓ VM {vm_name} created successfully")
            return True
        else:
            print(f"❌ Failed to create VM {vm_name}: {result.stderr}")
            return False

    def wait_for_vm_ready(self, vm_name: str, timeout: int = 300) -> bool:
        """Wait for VM to be ready and running."""
        print(f"Waiting for VM {vm_name} to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.run_command([
                "gcloud", "compute", "instances", "describe", vm_name,
                f"--project={self.project_id}",
                f"--zone={self.zone}",
                "--format=value(status)"
            ], check=False)
            
            if result.returncode == 0 and result.stdout.strip() == "RUNNING":
                print(f"✓ VM {vm_name} is running")
                time.sleep(30)  # Additional wait for startup script
                return True
            
            time.sleep(10)
        
        print(f"❌ VM {vm_name} did not become ready within {timeout} seconds")
        return False

    def get_vm_internal_ip(self, vm_name: str) -> str:
        """Get the internal IP address of a VM."""
        result = self.run_command([
            "gcloud", "compute", "instances", "describe", vm_name,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            "--format=value(networkInterfaces[0].networkIP)"
        ])
        return result.stdout.strip()

    def get_vm_external_ip(self, vm_name: str) -> str:
        """Get the external IP address of a VM."""
        result = self.run_command([
            "gcloud", "compute", "instances", "describe", vm_name,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            "--format=value(networkInterfaces[0].accessConfigs[0].natIP)"
        ], check=False)
        return result.stdout.strip() if result.returncode == 0 else ""

    def test_ssh_connectivity(self, vm_name: str) -> bool:
        """Test SSH connectivity to a VM."""
        print(f"Testing SSH connectivity to {vm_name}...")
        
        # Test SSH connection (works in dev containers)
        result = self.run_command([
            "gcloud", "compute", "ssh", vm_name,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            "--command=echo 'SSH test successful'",
            "--quiet",
            "--ssh-flag=-o ConnectTimeout=30",
            "--ssh-flag=-o StrictHostKeyChecking=no",
            "--ssh-flag=-o UserKnownHostsFile=/dev/null"
        ], check=False)
        
        if result.returncode == 0:
            print(f"✓ SSH connectivity to {vm_name} successful")
            return True
        else:
            print(f"❌ SSH connectivity to {vm_name} failed")
            return False

    def test_internal_connectivity(self, source_vm: str, target_vm: str, port: int = 22) -> bool:
        """Test internal connectivity between VMs."""
        print(f"Testing internal connectivity from {source_vm} to {target_vm} on port {port}...")
        
        target_ip = self.get_vm_internal_ip(target_vm)
        
        # Test connectivity using netcat (works in dev containers)
        result = self.run_command([
            "gcloud", "compute", "ssh", source_vm,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            f"--command=timeout 10 nc -zv {target_ip} {port}",
            "--quiet",
            "--ssh-flag=-o ConnectTimeout=30",
            "--ssh-flag=-o StrictHostKeyChecking=no",
            "--ssh-flag=-o UserKnownHostsFile=/dev/null"
        ], check=False)
        
        if result.returncode == 0:
            print(f"✓ Internal connectivity from {source_vm} to {target_vm}:{port} successful")
            return True
        else:
            print(f"❌ Internal connectivity from {source_vm} to {target_vm}:{port} failed")
            return False

    def test_internet_connectivity(self, vm_name: str) -> bool:
        """Test internet connectivity from a VM."""
        print(f"Testing internet connectivity from {vm_name}...")
        
        # Test connectivity to Google DNS (works in dev containers)
        result = self.run_command([
            "gcloud", "compute", "ssh", vm_name,
            f"--project={self.project_id}",
            f"--zone={self.zone}",
            "--command=timeout 10 curl -s http://www.google.com > /dev/null && echo 'Internet access successful'",
            "--quiet",
            "--ssh-flag=-o ConnectTimeout=30",
            "--ssh-flag=-o StrictHostKeyChecking=no",
            "--ssh-flag=-o UserKnownHostsFile=/dev/null"
        ], check=False)
        
        if result.returncode == 0 and "Internet access successful" in result.stdout:
            print(f"✓ Internet connectivity from {vm_name} successful")
            return True
        else:
            print(f"❌ Internet connectivity from {vm_name} failed")
            return False

    def test_firewall_rules(self) -> bool:
        """Test specific firewall rule functionality."""
        print("Testing firewall rules...")
        
        success = True
        
        # Test SSH access to web VM (should work)
        if not self.test_ssh_connectivity("web-test-vm"):
            success = False
        
        # Test internal connectivity between app and db VMs on database ports
        if not self.test_internal_connectivity("app-test-vm", "db-test-vm", 3306):  # MySQL
            success = False
        
        if not self.test_internal_connectivity("app-test-vm", "db-test-vm", 5432):  # PostgreSQL
            success = False
        
        # Test internal connectivity between all VMs (should work due to allow-internal rule)
        if not self.test_internal_connectivity("web-test-vm", "app-test-vm", 22):
            success = False
        
        if not self.test_internal_connectivity("app-test-vm", "web-test-vm", 22):
            success = False
        
        return success

    def test_nat_gateway(self) -> bool:
        """Test NAT gateway functionality for private VMs."""
        print("Testing NAT gateway functionality...")
        
        success = True
        
        # Private VMs should have internet access through NAT
        if not self.test_internet_connectivity("app-test-vm"):
            success = False
        
        if not self.test_internet_connectivity("db-test-vm"):
            success = False
        
        return success

    def cleanup_test_vms(self):
        """Clean up test VMs."""
        print("Cleaning up test VMs...")
        
        for vm_name in self.test_vms.keys():
            result = self.run_command([
                "gcloud", "compute", "instances", "delete", vm_name,
                f"--project={self.project_id}",
                f"--zone={self.zone}",
                "--quiet"
            ], check=False)
            
            if result.returncode == 0:
                print(f"✓ VM {vm_name} deleted")
            else:
                print(f"❌ Failed to delete VM {vm_name}")

    def run_connectivity_tests(self) -> bool:
        """Run all connectivity tests."""
        print("Starting VPC connectivity tests...\n")
        
        try:
            # Create test VMs
            print("Creating test VMs...")
            for vm_name, config in self.test_vms.items():
                if not self.create_test_vm(vm_name, config):
                    return False
                
                if not self.wait_for_vm_ready(vm_name):
                    return False
            
            print("\nRunning connectivity tests...")
            
            # Test firewall rules
            if not self.test_firewall_rules():
                return False
            
            # Test NAT gateway
            if not self.test_nat_gateway():
                return False
            
            print("\n🎉 All connectivity tests passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Connectivity test failed: {str(e)}")
            return False
        
        finally:
            # Always cleanup test VMs
            self.cleanup_test_vms()


if __name__ == "__main__":
    tester = ConnectivityTests()
    success = tester.run_connectivity_tests()
    sys.exit(0 if success else 1)
