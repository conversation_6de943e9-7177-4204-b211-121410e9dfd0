#!/usr/bin/env python3
"""
Test runner for all VPC infrastructure tests.
Runs infrastructure, connectivity, and Terraform validation tests.
"""

import sys
import argparse
from test_vpc_infrastructure import VPCInfrastructureTests
from test_terraform_validation import TerraformValidationTests
from test_connectivity import ConnectivityTests


def run_infrastructure_tests():
    """Run infrastructure validation tests."""
    print("=" * 60)
    print("RUNNING INFRASTRUCTURE TESTS")
    print("=" * 60)
    
    tester = VPCInfrastructureTests()
    return tester.run_all_tests()


def run_terraform_tests():
    """Run Terraform validation tests."""
    print("\n" + "=" * 60)
    print("RUNNING TERRAFORM VALIDATION TESTS")
    print("=" * 60)
    
    tester = TerraformValidationTests()
    return tester.run_all_tests()


def run_connectivity_tests():
    """Run connectivity tests."""
    print("\n" + "=" * 60)
    print("RUNNING CONNECTIVITY TESTS")
    print("=" * 60)
    print("⚠️  WARNING: This will create and delete test VMs")
    print("⚠️  This may incur small charges in your GCP project")
    
    response = input("Do you want to proceed with connectivity tests? (y/N): ")
    if response.lower() != 'y':
        print("Skipping connectivity tests")
        return True
    
    tester = ConnectivityTests()
    return tester.run_connectivity_tests()


def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="Run VPC infrastructure tests")
    parser.add_argument(
        "--test-type",
        choices=["all", "infrastructure", "terraform", "connectivity"],
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--skip-connectivity",
        action="store_true",
        help="Skip connectivity tests (which create VMs)"
    )
    
    args = parser.parse_args()
    
    print("VPC Infrastructure Test Suite")
    print("=" * 60)
    
    success = True
    
    if args.test_type in ["all", "infrastructure"]:
        if not run_infrastructure_tests():
            success = False
    
    if args.test_type in ["all", "terraform"]:
        if not run_terraform_tests():
            success = False
    
    if args.test_type in ["all", "connectivity"] and not args.skip_connectivity:
        if not run_connectivity_tests():
            success = False
    elif args.test_type == "connectivity" and args.skip_connectivity:
        print("Connectivity tests skipped by user request")
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("Your VPC infrastructure is correctly configured and working as expected.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please review the test output above and fix any issues.")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
