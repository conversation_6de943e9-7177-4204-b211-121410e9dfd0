#!/usr/bin/env python3
"""
Terraform validation tests for GCP VPC infrastructure.
Tests Terraform configuration, state, and plan validation.
"""

import json
import subprocess
import sys
import os
from typing import Dict, Any, List


class TerraformValidationTests:
    """Test class for Terraform configuration validation."""
    
    def __init__(self):
        # Auto-detect terraform directory (works in dev containers and locally)
        import os
        self.terraform_dir = os.getcwd()
        self.project_id = "p-dev-bt-concept-ywqb-1"

    def run_terraform_command(self, command: List[str]) -> subprocess.CompletedProcess:
        """Run a Terraform command and return the result."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                cwd=self.terraform_dir
            )
            return result
        except subprocess.CalledProcessError as e:
            print(f"Terraform command failed: {' '.join(command)}")
            print(f"Error: {e.stderr}")
            raise

    def test_terraform_validate(self) -> bool:
        """Test that Terraform configuration is valid."""
        print("Testing Terraform configuration validation...")
        
        try:
            result = self.run_terraform_command(["terraform", "validate"])

            if "Success! The configuration is valid." in result.stdout:
                print("✓ Terraform configuration is valid")
                return True
            else:
                print(f"❌ Terraform validation failed: {result.stdout}")
                return False

        except Exception as e:
            print(f"❌ Terraform validation error: {str(e)}")
            return False

    def test_terraform_fmt(self) -> bool:
        """Test that Terraform files are properly formatted."""
        print("Testing Terraform formatting...")
        
        try:
            result = self.run_terraform_command(["terraform", "fmt", "-check", "-diff"])
            
            if result.returncode == 0:
                print("✓ Terraform files are properly formatted")
                return True
            else:
                print(f"❌ Terraform files need formatting: {result.stdout}")
                return False
                
        except subprocess.CalledProcessError as e:
            if e.returncode == 3:  # fmt returns 3 when files need formatting
                print(f"❌ Terraform files need formatting: {e.stdout}")
                return False
            else:
                print(f"❌ Terraform fmt error: {e.stderr}")
                return False

    def test_terraform_plan_no_changes(self) -> bool:
        """Test that Terraform plan shows no changes (infrastructure matches state)."""
        print("Testing Terraform plan for drift detection...")
        
        try:
            result = self.run_terraform_command(["terraform", "plan", "-detailed-exitcode"])
            
            if result.returncode == 0:
                print("✓ No infrastructure drift detected")
                return True
            elif result.returncode == 2:
                print("❌ Infrastructure drift detected - plan shows changes needed")
                print(result.stdout)
                return False
            else:
                print(f"❌ Terraform plan failed with exit code {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            if e.returncode == 2:
                print("❌ Infrastructure drift detected - plan shows changes needed")
                print(e.stdout)
                return False
            else:
                print(f"❌ Terraform plan error: {e.stderr}")
                return False

    def test_terraform_state_consistency(self) -> bool:
        """Test that Terraform state is consistent."""
        print("Testing Terraform state consistency...")
        
        try:
            # Test state list
            result = self.run_terraform_command(["terraform", "state", "list"])
            resources = result.stdout.strip().split('\n')
            
            expected_resources = [
                "google_compute_network.vpc_network",
                "google_compute_router.router[\"us-central1\"]",
                "google_compute_router_nat.nat[\"us-central1\"]",
                "google_compute_subnetwork.subnets[\"web-subnet\"]",
                "google_compute_subnetwork.subnets[\"app-subnet\"]", 
                "google_compute_subnetwork.subnets[\"db-subnet\"]",
                "google_compute_subnetwork.subnets[\"gke-subnet\"]",
                "google_compute_firewall.allow_internal[0]",
                "google_compute_firewall.allow_ssh[0]",
                "google_compute_firewall.allow_http_https[0]",
                "google_compute_firewall.firewall_rules[\"allow-app-to-db\"]",
                "google_compute_firewall.firewall_rules[\"allow-load-balancer\"]"
            ]
            
            missing_resources = []
            for expected in expected_resources:
                if expected not in resources:
                    missing_resources.append(expected)
            
            if missing_resources:
                print(f"❌ Missing resources in state: {missing_resources}")
                return False
            
            print(f"✓ All {len(expected_resources)} expected resources found in state")
            return True
            
        except Exception as e:
            print(f"❌ Terraform state consistency error: {str(e)}")
            return False

    def test_terraform_outputs(self) -> bool:
        """Test that Terraform outputs are correct and accessible."""
        print("Testing Terraform outputs...")
        
        try:
            result = self.run_terraform_command(["terraform", "output", "-json"])
            outputs = json.loads(result.stdout)
            
            required_outputs = [
                "vpc_network_id",
                "vpc_network_name", 
                "subnet_ids",
                "subnet_cidr_ranges",
                "firewall_rule_ids",
                "default_firewall_rule_ids",
                "router_ids",
                "nat_gateway_ids",
                "network_info"
            ]
            
            missing_outputs = []
            for required in required_outputs:
                if required not in outputs:
                    missing_outputs.append(required)
            
            if missing_outputs:
                print(f"❌ Missing outputs: {missing_outputs}")
                return False
            
            # Validate specific output values
            if outputs["vpc_network_name"]["value"] != "main-vpc":
                print("❌ VPC network name output incorrect")
                return False
            
            if self.project_id not in outputs["vpc_network_id"]["value"]:
                print("❌ VPC network ID output incorrect")
                return False
            
            subnet_ids = outputs["subnet_ids"]["value"]
            expected_subnets = ["web-subnet", "app-subnet", "db-subnet", "gke-subnet"]
            
            for subnet in expected_subnets:
                if subnet not in subnet_ids:
                    print(f"❌ Subnet {subnet} not in outputs")
                    return False
            
            print("✓ All Terraform outputs are correct")
            return True
            
        except Exception as e:
            print(f"❌ Terraform outputs error: {str(e)}")
            return False

    def test_terraform_providers(self) -> bool:
        """Test that Terraform providers are properly configured."""
        print("Testing Terraform provider configuration...")
        
        try:
            result = self.run_terraform_command(["terraform", "providers"])
            
            if "hashicorp/google" in result.stdout:
                print("✓ Google provider is configured")
                return True
            else:
                print("❌ Google provider not found in configuration")
                return False
                
        except Exception as e:
            print(f"❌ Terraform providers error: {str(e)}")
            return False

    def test_terraform_workspace(self) -> bool:
        """Test that Terraform workspace is correct."""
        print("Testing Terraform workspace...")
        
        try:
            result = self.run_terraform_command(["terraform", "workspace", "show"])
            workspace = result.stdout.strip()
            
            if workspace == "default":
                print("✓ Using default Terraform workspace")
                return True
            else:
                print(f"ℹ️ Using workspace: {workspace}")
                return True
                
        except Exception as e:
            print(f"❌ Terraform workspace error: {str(e)}")
            return False

    def test_required_files_exist(self) -> bool:
        """Test that all required Terraform files exist."""
        print("Testing required Terraform files...")
        
        required_files = [
            "main.tf",
            "variables.tf", 
            "outputs.tf",
            "versions.tf",
            "terraform.tfvars",
            ".terraform.lock.hcl"
        ]
        
        missing_files = []
        for file in required_files:
            file_path = os.path.join(self.terraform_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Missing required files: {missing_files}")
            return False
        
        print("✓ All required Terraform files exist")
        return True

    def run_all_tests(self) -> bool:
        """Run all Terraform validation tests."""
        print("Starting Terraform validation tests...\n")
        
        try:
            tests = [
                self.test_required_files_exist,
                self.test_terraform_validate,
                self.test_terraform_fmt,
                self.test_terraform_providers,
                self.test_terraform_workspace,
                self.test_terraform_state_consistency,
                self.test_terraform_outputs,
                self.test_terraform_plan_no_changes
            ]
            
            for test in tests:
                if not test():
                    return False
            
            print("\n🎉 All Terraform validation tests passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Terraform validation failed: {str(e)}")
            return False


if __name__ == "__main__":
    tester = TerraformValidationTests()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
