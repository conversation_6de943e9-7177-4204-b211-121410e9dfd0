# Main Terraform configuration for GCP VPC automation

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Primary VPC Network Module
module "vpc_network" {
  source = "./modules/vpc"

  project_id            = var.project_id
  vpc_name              = var.vpc_name
  vpc_mtu               = var.vpc_mtu
  delete_default_routes = var.delete_default_routes
}

# Primary Subnets Module
module "subnets" {
  source = "./modules/subnets"

  project_id  = var.project_id
  vpc_network = module.vpc_network.vpc_network
  subnets     = var.subnets
}

# Primary Cloud NAT Module
module "cloud_nat" {
  source = "./modules/cloud-nat"

  project_id     = var.project_id
  vpc_name       = var.vpc_name
  vpc_network    = module.vpc_network.vpc_network
  enable_nat     = var.enable_nat
  router_asn     = var.router_asn
  subnet_regions = module.subnets.subnet_regions
}

# Primary Firewall Module
module "firewall" {
  source = "./modules/firewall"

  project_id                    = var.project_id
  vpc_name                      = var.vpc_name
  vpc_network                   = module.vpc_network.vpc_network
  firewall_rules                = var.firewall_rules
  create_default_firewall_rules = var.create_default_firewall_rules
  ssh_source_ranges             = var.ssh_source_ranges
  subnets                       = var.subnets
}

# Secondary VPC Network Module
module "vpc_network_secondary" {
  source = "./modules/vpc"

  project_id            = var.project_id
  vpc_name              = var.secondary_vpc_name
  vpc_mtu               = var.vpc_mtu
  delete_default_routes = var.delete_default_routes
}

# Secondary Subnets Module
module "subnets_secondary" {
  source = "./modules/subnets"

  project_id  = var.project_id
  vpc_network = module.vpc_network_secondary.vpc_network
  subnets     = var.secondary_subnets
}

# Secondary Cloud NAT Module
module "cloud_nat_secondary" {
  source = "./modules/cloud-nat"

  project_id     = var.project_id
  vpc_name       = var.secondary_vpc_name
  vpc_network    = module.vpc_network_secondary.vpc_network
  enable_nat     = var.enable_nat
  router_asn     = var.router_asn
  subnet_regions = module.subnets_secondary.subnet_regions
}

# Secondary Firewall Module
module "firewall_secondary" {
  source = "./modules/firewall"

  project_id                    = var.project_id
  vpc_name                      = var.secondary_vpc_name
  vpc_network                   = module.vpc_network_secondary.vpc_network
  firewall_rules                = var.secondary_firewall_rules
  create_default_firewall_rules = var.create_default_firewall_rules
  ssh_source_ranges             = var.ssh_source_ranges
  subnets                       = var.secondary_subnets
}
