# Main Terraform configuration for GCP VPC automation

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}


# Derive distinct regions from provided subnets for multi-region support
locals {
  subnet_regions = distinct([for s in values(var.subnets) : s.region])
}

# Create VPC Network
resource "google_compute_network" "vpc_network" {
  name                    = var.vpc_name
  auto_create_subnetworks = false
  mtu                     = var.vpc_mtu
  description             = "VPC network created by Terraform"

  # Enable deletion protection for production environments
  delete_default_routes_on_create = var.delete_default_routes
}

# Create subnets
resource "google_compute_subnetwork" "subnets" {
  for_each = var.subnets

  name          = each.key
  ip_cidr_range = each.value.cidr
  region        = each.value.region
  network       = google_compute_network.vpc_network.id
  description   = each.value.description

  # Enable private Google access if specified
  private_ip_google_access = each.value.private_google_access

  # Secondary IP ranges for GKE clusters if specified
  dynamic "secondary_ip_range" {
    for_each = each.value.secondary_ranges
    content {
      range_name    = secondary_ip_range.value.range_name
      ip_cidr_range = secondary_ip_range.value.ip_cidr_range
    }
  }

  # Enable flow logs if specified
  dynamic "log_config" {
    for_each = each.value.enable_flow_logs ? [1] : []
    content {
      aggregation_interval = "INTERVAL_10_MIN"
      flow_sampling        = 0.5
      metadata             = "INCLUDE_ALL_METADATA"
    }
  }
}

# Create Cloud Routers per region for NAT Gateway
resource "google_compute_router" "router" {
  for_each = var.enable_nat ? toset(local.subnet_regions) : toset([])

  name    = "${var.vpc_name}-router"
  region  = each.key
  network = google_compute_network.vpc_network.id

  bgp {
    asn = var.router_asn
  }
}

# Create Cloud NAT per region
resource "google_compute_router_nat" "nat" {
  for_each = var.enable_nat ? toset(local.subnet_regions) : toset([])

  name                               = "${var.vpc_name}-nat"
  router                             = google_compute_router.router[each.key].name
  region                             = each.key
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# Create firewall rules
resource "google_compute_firewall" "firewall_rules" {
  for_each = var.firewall_rules

  name    = each.key
  network = google_compute_network.vpc_network.name

  allow {
    protocol = each.value.protocol
    ports    = each.value.ports
  }

  source_ranges = each.value.source_ranges
  target_tags   = each.value.target_tags
  description   = each.value.description
}

# Default firewall rules
resource "google_compute_firewall" "allow_internal" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-internal"
  network = google_compute_network.vpc_network.name

  allow {
    protocol = "icmp"
  }

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  source_ranges = [for subnet in var.subnets : subnet.cidr]
  description   = "Allow internal communication within VPC"
}

resource "google_compute_firewall" "allow_ssh" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-ssh"
  network = google_compute_network.vpc_network.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = var.ssh_source_ranges
  target_tags   = ["ssh-allowed"]
  description   = "Allow SSH access"
}

resource "google_compute_firewall" "allow_http_https" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-http-https"
  network = google_compute_network.vpc_network.name

  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["http-server", "https-server"]
  description   = "Allow HTTP and HTTPS traffic"
}
