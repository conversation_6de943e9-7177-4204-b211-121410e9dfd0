# Outputs for GCP VPC Terraform configuration

# Primary VPC Outputs
output "vpc_network_id" {
  description = "The ID of the primary VPC network"
  value       = module.vpc_network.vpc_network_id
}

output "vpc_network_name" {
  description = "The name of the primary VPC network"
  value       = module.vpc_network.vpc_network_name
}

output "vpc_network_self_link" {
  description = "The self-link of the primary VPC network"
  value       = module.vpc_network.vpc_network_self_link
}

# Secondary VPC Outputs
output "secondary_vpc_network_id" {
  description = "The ID of the secondary VPC network"
  value       = module.vpc_network_secondary.vpc_network_id
}

output "secondary_vpc_network_name" {
  description = "The name of the secondary VPC network"
  value       = module.vpc_network_secondary.vpc_network_name
}

output "secondary_vpc_network_self_link" {
  description = "The self-link of the secondary VPC network"
  value       = module.vpc_network_secondary.vpc_network_self_link
}

output "subnet_ids" {
  description = "Map of primary subnet names to their IDs"
  value       = module.subnets.subnet_ids
}

output "subnet_self_links" {
  description = "Map of primary subnet names to their self-links"
  value       = module.subnets.subnet_self_links
}

output "subnet_cidr_ranges" {
  description = "Map of primary subnet names to their CIDR ranges"
  value       = module.subnets.subnet_cidr_ranges
}

output "subnet_gateway_addresses" {
  description = "Map of primary subnet names to their gateway addresses"
  value       = module.subnets.subnet_gateway_addresses
}

output "secondary_subnet_ids" {
  description = "Map of secondary subnet names to their IDs"
  value       = module.subnets_secondary.subnet_ids
}

output "secondary_subnet_self_links" {
  description = "Map of secondary subnet names to their self-links"
  value       = module.subnets_secondary.subnet_self_links
}

output "secondary_subnet_cidr_ranges" {
  description = "Map of secondary subnet names to their CIDR ranges"
  value       = module.subnets_secondary.subnet_cidr_ranges
}

output "secondary_subnet_gateway_addresses" {
  description = "Map of secondary subnet names to their gateway addresses"
  value       = module.subnets_secondary.subnet_gateway_addresses
}

output "router_ids" {
  description = "Map of primary router names to their IDs"
  value       = module.cloud_nat.router_ids
}

output "nat_gateway_ids" {
  description = "Map of primary NAT gateway names to their IDs"
  value       = module.cloud_nat.nat_gateway_ids
}

output "firewall_rule_ids" {
  description = "Map of primary firewall rule names to their IDs"
  value       = module.firewall.firewall_rule_ids
}

output "default_firewall_rule_ids" {
  description = "IDs of primary default firewall rules"
  value       = module.firewall.default_firewall_rule_ids
}

output "secondary_router_ids" {
  description = "Map of secondary router names to their IDs"
  value       = module.cloud_nat_secondary.router_ids
}

output "secondary_nat_gateway_ids" {
  description = "Map of secondary NAT gateway names to their IDs"
  value       = module.cloud_nat_secondary.nat_gateway_ids
}

output "secondary_firewall_rule_ids" {
  description = "Map of secondary firewall rule names to their IDs"
  value       = module.firewall_secondary.firewall_rule_ids
}

output "secondary_default_firewall_rule_ids" {
  description = "IDs of secondary default firewall rules"
  value       = module.firewall_secondary.default_firewall_rule_ids
}

# Network information for use by other modules
output "network_info" {
  description = "Complete primary network information for use by other modules"
  value = {
    network_id        = module.vpc_network.vpc_network_id
    network_name      = module.vpc_network.vpc_network_name
    network_self_link = module.vpc_network.vpc_network_self_link
    subnets = {
      for name, subnet in module.subnets.subnets : name => {
        id              = subnet.id
        self_link       = subnet.self_link
        cidr_range      = subnet.ip_cidr_range
        region          = subnet.region
        gateway_address = subnet.gateway_address
      }
    }
  }
}

output "secondary_network_info" {
  description = "Complete secondary network information for use by other modules"
  value = {
    network_id        = module.vpc_network_secondary.vpc_network_id
    network_name      = module.vpc_network_secondary.vpc_network_name
    network_self_link = module.vpc_network_secondary.vpc_network_self_link
    subnets = {
      for name, subnet in module.subnets_secondary.subnets : name => {
        id              = subnet.id
        self_link       = subnet.self_link
        cidr_range      = subnet.ip_cidr_range
        region          = subnet.region
        gateway_address = subnet.gateway_address
      }
    }
  }
}
