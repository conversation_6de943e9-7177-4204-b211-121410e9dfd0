# Outputs for GCP VPC Terraform configuration

output "vpc_network_id" {
  description = "The ID of the VPC network"
  value       = google_compute_network.vpc_network.id
}

output "vpc_network_name" {
  description = "The name of the VPC network"
  value       = google_compute_network.vpc_network.name
}

output "vpc_network_self_link" {
  description = "The self-link of the VPC network"
  value       = google_compute_network.vpc_network.self_link
}

output "subnet_ids" {
  description = "Map of subnet names to their IDs"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.id
  }
}

output "subnet_self_links" {
  description = "Map of subnet names to their self-links"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.self_link
  }
}

output "subnet_cidr_ranges" {
  description = "Map of subnet names to their CIDR ranges"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.ip_cidr_range
  }
}

output "subnet_gateway_addresses" {
  description = "Map of subnet names to their gateway addresses"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.gateway_address
  }
}

output "router_ids" {
  description = "Map of router names to their IDs"
  value = {
    for name, router in google_compute_router.router : name => router.id
  }
}

output "nat_gateway_ids" {
  description = "Map of NAT gateway names to their IDs"
  value = {
    for name, nat in google_compute_router_nat.nat : name => nat.id
  }
}

output "firewall_rule_ids" {
  description = "Map of firewall rule names to their IDs"
  value = {
    for name, rule in google_compute_firewall.firewall_rules : name => rule.id
  }
}

output "default_firewall_rule_ids" {
  description = "IDs of default firewall rules"
  value = {
    allow_internal = var.create_default_firewall_rules ? google_compute_firewall.allow_internal[0].id : null
    allow_ssh      = var.create_default_firewall_rules ? google_compute_firewall.allow_ssh[0].id : null
    allow_http_https = var.create_default_firewall_rules ? google_compute_firewall.allow_http_https[0].id : null
  }
}

# Network information for use by other modules
output "network_info" {
  description = "Complete network information for use by other modules"
  value = {
    network_id   = google_compute_network.vpc_network.id
    network_name = google_compute_network.vpc_network.name
    network_self_link = google_compute_network.vpc_network.self_link
    subnets = {
      for name, subnet in google_compute_subnetwork.subnets : name => {
        id           = subnet.id
        self_link    = subnet.self_link
        cidr_range   = subnet.ip_cidr_range
        region       = subnet.region
        gateway_address = subnet.gateway_address
      }
    }
  }
}
