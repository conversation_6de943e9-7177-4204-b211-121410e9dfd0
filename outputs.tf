# Outputs for GCP VPC Terraform configuration

output "vpc_network_id" {
  description = "The ID of the VPC network"
  value       = module.vpc_network.vpc_network_id
}

output "vpc_network_name" {
  description = "The name of the VPC network"
  value       = module.vpc_network.vpc_network_name
}

output "vpc_network_self_link" {
  description = "The self-link of the VPC network"
  value       = module.vpc_network.vpc_network_self_link
}

output "subnet_ids" {
  description = "Map of subnet names to their IDs"
  value       = module.subnets.subnet_ids
}

output "subnet_self_links" {
  description = "Map of subnet names to their self-links"
  value       = module.subnets.subnet_self_links
}

output "subnet_cidr_ranges" {
  description = "Map of subnet names to their CIDR ranges"
  value       = module.subnets.subnet_cidr_ranges
}

output "subnet_gateway_addresses" {
  description = "Map of subnet names to their gateway addresses"
  value       = module.subnets.subnet_gateway_addresses
}

output "router_ids" {
  description = "Map of router names to their IDs"
  value       = module.cloud_nat.router_ids
}

output "nat_gateway_ids" {
  description = "Map of NAT gateway names to their IDs"
  value       = module.cloud_nat.nat_gateway_ids
}

output "firewall_rule_ids" {
  description = "Map of firewall rule names to their IDs"
  value       = module.firewall.firewall_rule_ids
}

output "default_firewall_rule_ids" {
  description = "IDs of default firewall rules"
  value       = module.firewall.default_firewall_rule_ids
}

# Network information for use by other modules
output "network_info" {
  description = "Complete network information for use by other modules"
  value = {
    network_id        = module.vpc_network.vpc_network_id
    network_name      = module.vpc_network.vpc_network_name
    network_self_link = module.vpc_network.vpc_network_self_link
    subnets = {
      for name, subnet in module.subnets.subnets : name => {
        id              = subnet.id
        self_link       = subnet.self_link
        cidr_range      = subnet.ip_cidr_range
        region          = subnet.region
        gateway_address = subnet.gateway_address
      }
    }
  }
}
