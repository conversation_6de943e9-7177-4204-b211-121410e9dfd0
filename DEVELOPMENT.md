# Development Environment Setup

This document provides comprehensive instructions for setting up the GCP VPC Terraform development environment using different approaches.

## 🚀 Quick Start with Dev Container (Recommended)

The fastest way to get started is using the pre-configured dev container:

### Prerequisites
- [VS Code](https://code.visualstudio.com/)
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
- [Docker Desktop](https://www.docker.com/products/docker-desktop)

### Setup Steps

1. **Open in Dev Container**:
   ```bash
   code .
   # Click "Reopen in Container" when prompted
   ```

2. **Authenticate and Configure**:
   ```bash
   # Inside the dev container
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Run Setup**:
   ```bash
   make dev-setup
   ```

4. **Deploy Infrastructure**:
   ```bash
   make plan
   make apply
   ```

## 🐳 Alternative: Docker Compose

For development without VS Code:

```bash
# Start development container
docker-compose up -d terraform-dev

# Enter container
docker-compose exec terraform-dev bash

# Run setup
make dev-setup
```

## 💻 Local Development

For direct installation on your machine:

### Install Dependencies

**macOS (using Homebrew)**:
```bash
brew install terraform
brew install --cask google-cloud-sdk
brew install python3
```

**Ubuntu/Debian**:
```bash
# Terraform
wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | sudo tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
sudo apt update && sudo apt install terraform

# Google Cloud SDK
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key add -
echo "deb https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
sudo apt update && sudo apt install google-cloud-cli

# Python
sudo apt install python3 python3-pip
```

### Setup and Run
```bash
# Install Python dependencies
make install-test-deps

# Setup environment
make setup

# Deploy infrastructure
make plan
make apply
```

## 🛠️ Development Tools and Commands

### Available Make Targets

| Command | Description |
|---------|-------------|
| `make help` | Show all available commands |
| `make dev-setup` | Set up development environment |
| `make dev-status` | Show environment status |
| `make setup` | Complete setup (auth, APIs, init) |
| `make plan` | Create Terraform execution plan |
| `make apply` | Apply Terraform configuration |
| `make destroy` | Destroy infrastructure |
| `make test` | Run all tests |
| `make test-safe` | Run tests without creating VMs |
| `make validate` | Validate Terraform configuration |
| `make format` | Format Terraform files |

### Shell Aliases (in Dev Container)

```bash
# Terraform
tf, tfi, tfp, tfa, tfd, tfo, tfv, tff

# GCP
gcl, gcla, gclp, gcls

# Testing
test-vpc, test-all

# Status
vpc-status
```

## 🧪 Testing

### Test Types

1. **Infrastructure Tests**: Validate deployed resources
2. **Terraform Tests**: Validate configuration and state
3. **Connectivity Tests**: Test network functionality (creates VMs)

### Running Tests

```bash
# Safe tests (no VM creation)
make test-safe

# All tests (including connectivity)
make test

# Specific test types
make test-infra
make test-terraform
make test-connectivity
```

## 📁 Project Structure

```
.
├── .devcontainer/          # Dev container configuration
│   ├── devcontainer.json   # VS Code dev container config
│   └── setup.sh           # Container setup script
├── docs/                   # Documentation
├── scripts/               # Helper scripts
├── tests/                 # Test suite
│   ├── test_vpc_infrastructure.py
│   ├── test_terraform_validation.py
│   ├── test_connectivity.py
│   └── run_all_tests.py
├── main.tf               # Main Terraform configuration
├── variables.tf          # Variable definitions
├── outputs.tf           # Output definitions
├── versions.tf          # Provider requirements
├── terraform.tfvars.example  # Example variables
├── Makefile             # Automation commands
├── docker-compose.yml   # Docker Compose setup
└── Dockerfile.dev       # Development container image
```

## 🔧 Configuration

### Environment Variables

The dev container sets up these environment variables:
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to GCP credentials
- `TF_LOG_PATH`: Terraform log file location

### GCP Project Configuration

1. **Set Project ID** in `terraform.tfvars`:
   ```hcl
   project_id = "your-gcp-project-id"
   ```

2. **Configure Regions/Zones**:
   ```hcl
   region = "us-central1"
   zone   = "us-central1-a"
   ```

3. **Customize Subnets**:
   ```hcl
   subnets = {
     "custom-subnet" = {
       cidr                  = "********/24"
       region               = "us-central1"
       description          = "Custom subnet"
       private_google_access = true
       enable_flow_logs     = true
       secondary_ranges     = []
     }
   }
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Errors**:
   ```bash
   gcloud auth login
   gcloud auth application-default login
   ```

2. **Project Not Set**:
   ```bash
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Terraform State Issues**:
   ```bash
   terraform refresh
   # or
   terraform init -reconfigure
   ```

4. **Dev Container Issues**:
   - Rebuild container: Command Palette → "Dev Containers: Rebuild Container"
   - Check Docker Desktop is running
   - Verify VS Code Dev Containers extension is installed

### Debug Mode

Enable debug logging:
```bash
export TF_LOG=DEBUG
export TF_LOG_PATH=./logs/terraform-debug.log
terraform plan
```

## 🚀 Deployment Workflow

### Development Cycle

1. **Make Changes**: Edit Terraform files
2. **Validate**: `make validate`
3. **Format**: `make format`
4. **Plan**: `make plan`
5. **Test**: `make test-safe`
6. **Apply**: `make apply`
7. **Verify**: `make test`

### Environment Management

```bash
# Use Terraform workspaces for different environments
terraform workspace new development
terraform workspace new staging
terraform workspace new production

# Switch environments
terraform workspace select development
```

## 📚 Additional Resources

- [Dev Container Guide](docs/DEV_CONTAINER_GUIDE.md)
- [Test Documentation](tests/README.md)
- [Terraform Best Practices](https://www.terraform.io/docs/cloud/guides/recommended-practices/index.html)
- [GCP VPC Documentation](https://cloud.google.com/vpc/docs)

## 🤝 Contributing

1. Use the dev container for consistent environment
2. Run tests before submitting changes
3. Follow Terraform formatting standards
4. Update documentation for any changes
5. Test on multiple environments when possible

## 📞 Support

For issues or questions:
1. Check this documentation
2. Review error logs in `./logs/`
3. Run `make dev-status` to check environment
4. Create an issue in the repository
