#!/bin/bash

# Simple dev container setup script for GCP VPC Terraform development

set -e

echo "🚀 Setting up GCP VPC Terraform development environment (Simple)..."

# Update package lists
sudo apt-get update

# Install additional tools
sudo apt-get install -y \
    curl \
    wget \
    unzip \
    jq \
    tree \
    htop \
    vim \
    git \
    make \
    netcat-openbsd \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install Google Cloud CLI
echo "📦 Installing Google Cloud CLI..."
curl -sSL https://sdk.cloud.google.com | bash
echo 'source /home/<USER>/google-cloud-sdk/path.bash.inc' >> ~/.bashrc
echo 'source /home/<USER>/google-cloud-sdk/completion.bash.inc' >> ~/.bashrc

# Install Python dependencies
echo "📦 Installing Python dependencies..."
if [ -f tests/requirements.txt ]; then
    pip3 install --user -r tests/requirements.txt
    echo "✅ Python dependencies installed"
else
    echo "⚠️  tests/requirements.txt not found, skipping Python dependencies"
fi

# Verify installations
echo "🔍 Verifying installations..."
echo "Terraform version:"
terraform version

echo "Python version:"
python3 --version

# Set up shell aliases and functions
echo "⚙️ Setting up shell configuration..."
cat >> ~/.bashrc << 'EOF'

# GCP VPC Terraform aliases
alias tf='terraform'
alias tfi='terraform init'
alias tfp='terraform plan'
alias tfa='terraform apply'
alias tfd='terraform destroy'
alias tfo='terraform output'
alias tfv='terraform validate'
alias tff='terraform fmt'

# GCP aliases (will work after gcloud is sourced)
alias gcl='gcloud'
alias gcla='gcloud auth list'
alias gclp='gcloud config list project'
alias gcls='gcloud config set project'

# Test aliases
alias test-vpc='make test-safe'
alias test-all='make test'

# Utility functions
function gcp-project() {
    if [ $# -eq 0 ]; then
        gcloud config get-value project
    else
        gcloud config set project $1
    fi
}

function vpc-status() {
    echo "=== Current Configuration ==="
    echo "GCP Project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
    echo "Terraform Workspace: $(terraform workspace show 2>/dev/null || echo 'Not initialized')"
    echo "Authentication: $(gcloud auth list --filter=status:ACTIVE --format='value(account)' 2>/dev/null || echo 'Not authenticated')"
    echo ""
    
    if [ -f terraform.tfvars ]; then
        echo "=== Terraform Variables ==="
        grep -E '^[^#]*=' terraform.tfvars | head -10
        echo ""
    fi
    
    if [ -f .terraform.lock.hcl ]; then
        echo "=== Infrastructure Status ==="
        terraform show -json 2>/dev/null | jq -r '.values.root_module.resources[]?.type' | sort | uniq -c 2>/dev/null || echo "Run 'terraform init' first"
    fi
}

EOF

# Create workspace directories
mkdir -p ~/workspace/logs

echo "✅ Dev container setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Run: source ~/.bashrc"
echo "2. Run: gcloud auth login"
echo "3. Run: gcloud auth application-default login"
echo "4. Run: gcloud config set project YOUR_PROJECT_ID"
echo "5. Run: vpc-status"
echo ""
echo "📚 Available commands:"
echo "  vpc-status     - Show current configuration status"
echo "  make setup     - Full setup with auth and API checks"
echo "  make test-safe - Run tests without creating VMs"
echo ""
echo "🔧 Terraform aliases: tf, tfi, tfp, tfa, tfd, tfo, tfv, tff"
echo "🌐 GCP aliases: gcl, gcla, gclp, gcls"
