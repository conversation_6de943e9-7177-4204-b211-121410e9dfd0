#!/bin/bash

# Dev container setup script for GCP VPC Terraform development

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Source sudo utility functions
if [ -f "$PROJECT_ROOT/scripts/sudo-utils.sh" ]; then
    source "$PROJECT_ROOT/scripts/sudo-utils.sh"
else
    echo "❌ sudo-utils.sh not found. Please ensure scripts/sudo-utils.sh exists."
    exit 1
fi

echo "🚀 Setting up GCP VPC Terraform development environment..."

# Show sudo status
show_sudo_status

# Detect package manager
PACKAGE_MANAGER=$(detect_package_manager)
echo "📦 Detected package manager: $PACKAGE_MANAGER"

# Install additional tools
echo "📦 Installing system packages..."
install_packages "$PACKAGE_MANAGER" \
    curl \
    wget \
    unzip \
    jq \
    tree \
    htop \
    vim \
    git \
    make \
    netcat-openbsd \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install Google Cloud CLI if not already installed
if ! command -v gcloud &> /dev/null; then
    echo "📦 Installing Google Cloud CLI..."

    # Add Google Cloud SDK repository
    echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | run_with_sudo_if_needed tee -a /etc/apt/sources.list.d/google-cloud-sdk.list

    # Import Google Cloud public key
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | run_with_sudo_if_needed gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg

    # Update and install
    run_with_sudo_if_needed apt-get update
    install_packages "$PACKAGE_MANAGER" google-cloud-cli

    echo "✅ Google Cloud CLI installed"
else
    echo "✅ Google Cloud CLI already installed"
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
if [ -f tests/requirements.txt ]; then
    install_packages "pip3" "-r tests/requirements.txt"
    echo "✅ Python dependencies installed"
else
    echo "⚠️  tests/requirements.txt not found, skipping Python dependencies"
fi

# Verify installations
echo "🔍 Verifying installations..."
echo "Terraform version:"
terraform version

echo "Google Cloud SDK version:"
gcloud version

echo "Python version:"
python3 --version

echo "Docker version:"
docker --version

# Set up shell aliases and functions
echo "⚙️ Setting up shell configuration..."
cat >> ~/.bashrc << 'EOF'

# GCP VPC Terraform aliases
alias tf='terraform'
alias tfi='terraform init'
alias tfp='terraform plan'
alias tfa='terraform apply'
alias tfd='terraform destroy'
alias tfo='terraform output'
alias tfv='terraform validate'
alias tff='terraform fmt'

# GCP aliases
alias gcl='gcloud'
alias gcla='gcloud auth list'
alias gclp='gcloud config list project'
alias gcls='gcloud config set project'

# Test aliases
alias test-vpc='make test-safe'
alias test-all='make test'
alias test-infra='make test-infra'
alias test-terraform='make test-terraform'

# Utility functions
function gcp-project() {
    if [ $# -eq 0 ]; then
        gcloud config get-value project
    else
        gcloud config set project $1
    fi
}

function tf-workspace() {
    if [ $# -eq 0 ]; then
        terraform workspace show
    else
        terraform workspace select $1 2>/dev/null || terraform workspace new $1
    fi
}

function vpc-status() {
    echo "=== Current Configuration ==="
    echo "GCP Project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
    echo "Terraform Workspace: $(terraform workspace show 2>/dev/null || echo 'Not initialized')"
    echo "Authentication: $(gcloud auth list --filter=status:ACTIVE --format='value(account)' 2>/dev/null || echo 'Not authenticated')"
    echo ""
    
    if [ -f terraform.tfvars ]; then
        echo "=== Terraform Variables ==="
        grep -E '^[^#]*=' terraform.tfvars | head -10
        echo ""
    fi
    
    if [ -f .terraform.lock.hcl ]; then
        echo "=== Infrastructure Status ==="
        terraform show -json 2>/dev/null | jq -r '.values.root_module.resources[]?.type' | sort | uniq -c 2>/dev/null || echo "Run 'terraform init' first"
    fi
}

EOF

# Create helpful scripts
echo "📝 Creating helper scripts..."

# Create a quick setup script
cat > ~/quick-setup.sh << 'EOF'
#!/bin/bash
echo "🔧 Quick GCP VPC Setup"
echo "====================="

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with GCP. Run: gcloud auth login"
    exit 1
fi

# Check if project is set
PROJECT=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT" ]; then
    echo "❌ No GCP project set. Run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

echo "✅ GCP Project: $PROJECT"

# Check if terraform.tfvars exists
if [ ! -f terraform.tfvars ]; then
    echo "📝 Creating terraform.tfvars from example..."
    cp terraform.tfvars.example terraform.tfvars
    sed -i "s/your-gcp-project-id/$PROJECT/g" terraform.tfvars
    echo "✅ Created terraform.tfvars with project: $PROJECT"
fi

# Initialize Terraform if needed
if [ ! -f .terraform.lock.hcl ]; then
    echo "🔧 Initializing Terraform..."
    terraform init
fi

# Set up application default credentials
if [ ! -f ~/.config/gcloud/application_default_credentials.json ]; then
    echo "🔑 Setting up application default credentials..."
    gcloud auth application-default login
fi

echo "🎉 Setup complete! You can now run:"
echo "  make plan    # Create execution plan"
echo "  make apply   # Deploy infrastructure"
echo "  make test    # Run all tests"
echo "  vpc-status   # Check current status"
EOF

chmod +x ~/quick-setup.sh

# Set up git configuration if not already set
if [ -z "$(git config --global user.name)" ]; then
    echo "📝 Setting up git configuration..."
    git config --global user.name "Dev Container User"
    git config --global user.email "<EMAIL>"
    git config --global init.defaultBranch main
fi

# Create workspace directories
mkdir -p ~/workspace/logs
mkdir -p ~/workspace/backups

# Set up logging for terraform operations
cat > ~/workspace/tf-with-logging.sh << 'EOF'
#!/bin/bash
# Terraform wrapper with logging
LOGDIR=~/workspace/logs
mkdir -p $LOGDIR
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOGFILE="$LOGDIR/terraform_${1}_$TIMESTAMP.log"

echo "Running: terraform $@" | tee $LOGFILE
terraform "$@" 2>&1 | tee -a $LOGFILE
RESULT=${PIPESTATUS[0]}

if [ $RESULT -eq 0 ]; then
    echo "✅ Command completed successfully" | tee -a $LOGFILE
else
    echo "❌ Command failed with exit code $RESULT" | tee -a $LOGFILE
fi

echo "Log saved to: $LOGFILE"
exit $RESULT
EOF

chmod +x ~/workspace/tf-with-logging.sh

# Add to PATH
echo 'export PATH="$HOME/workspace:$PATH"' >> ~/.bashrc

echo "✅ Dev container setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Run: source ~/.bashrc"
echo "2. Run: ~/quick-setup.sh"
echo "3. Run: vpc-status"
echo ""
echo "📚 Available commands:"
echo "  vpc-status     - Show current configuration status"
echo "  quick-setup.sh - Quick setup for new projects"
echo "  make setup     - Full setup with auth and API checks"
echo "  make test-safe - Run tests without creating VMs"
echo ""
echo "🔧 Terraform aliases: tf, tfi, tfp, tfa, tfd, tfo, tfv, tff"
echo "🌐 GCP aliases: gcl, gcla, gclp, gcls"
