# Example Terraform variables file
# Copy this file to terraform.tfvars and customize the values

# Required variables
project_id = "gcp-sandbox-334609"

# Optional variables with example values
region = "europe-west2"     # London
zone   = "europe-west2-a"   # London

vpc_name = "main-vpc"
vpc_mtu  = 1460

# Subnet configuration
subnets = {
  "web-subnet" = {
    cidr                  = "********/24"
    region               = "europe-west2"   # London
    description          = "Public subnet for web servers (London)"
    private_google_access = false
    enable_flow_logs     = true
    secondary_ranges     = []
  }
  "app-subnet" = {
    cidr                  = "********/24"
    region               = "us-east1"
    description          = "Private subnet for application servers (east)"
    private_google_access = true
    enable_flow_logs     = true
    secondary_ranges     = []
  }
  "db-subnet" = {
    cidr                  = "********/24"
    region               = "us-east1"
    description          = "Private subnet for databases (east)"
    private_google_access = true
    enable_flow_logs     = true
    secondary_ranges     = []
  }
  "gke-subnet" = {
    cidr                  = "********/24"
    region               = "europe-west4"   # Amsterdam
    description          = "Subnet for GKE cluster (Amsterdam)"
    private_google_access = true
    enable_flow_logs     = true
    secondary_ranges = [
      {
        range_name    = "gke-pods"
        ip_cidr_range = "********/16"
      },
      {
        range_name    = "gke-services"
        ip_cidr_range = "********/16"
      }
    ]
  }
}

# NAT Gateway configuration
enable_nat = true
router_asn = 64514

# Firewall rules
create_default_firewall_rules = true
ssh_source_ranges = ["0.0.0.0/0"]  # Restrict this in production

# Custom firewall rules
firewall_rules = {
  "allow-app-to-db" = {
    protocol      = "tcp"
    ports         = ["3306", "5432"]
    source_ranges = ["********/24"]  # app-subnet
    target_tags   = ["database"]
    description   = "Allow app servers to access databases"
  }
  "allow-load-balancer" = {
    protocol      = "tcp"
    ports         = ["80", "443", "8080"]
    source_ranges = ["***********/22", "**********/16"]  # Google Load Balancer ranges
    target_tags   = ["web-server"]
    description   = "Allow Google Load Balancer health checks"
  }
}

# Advanced configurations (optional)
enable_private_service_connect = false

dns_policy = {
  enable_inbound_forwarding = false
  enable_logging           = false
  networks                 = []
}

peering_connections = {}

tags = {
  environment = "production"
  team        = "infrastructure"
  project     = "main-application"
}
