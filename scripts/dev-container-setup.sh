#!/bin/bash

# Development container setup script
# This script sets up the GCP VPC Terraform environment in a dev container

set -e

echo "🚀 Setting up GCP VPC Terraform in dev container..."

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo "🔍 Checking required tools..."
MISSING_TOOLS=()

if ! command_exists terraform; then
    MISSING_TOOLS+=("terraform")
fi

if ! command_exists gcloud; then
    MISSING_TOOLS+=("gcloud")
fi

if ! command_exists python3; then
    MISSING_TOOLS+=("python3")
fi

if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
    echo "❌ Missing required tools: ${MISSING_TOOLS[*]}"
    echo "Please ensure your dev container has all required tools installed."
    exit 1
fi

echo "✅ All required tools are available"

# Set up Python environment
echo "📦 Setting up Python environment..."
if [ ! -f tests/requirements.txt ]; then
    echo "❌ tests/requirements.txt not found"
    exit 1
fi

python3 -m pip install --user -r tests/requirements.txt
echo "✅ Python dependencies installed"

# Check GCP authentication
echo "🔑 Checking GCP authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "⚠️  Not authenticated with GCP"
    echo "Run the following commands to authenticate:"
    echo "  gcloud auth login"
    echo "  gcloud auth application-default login"
    echo "  gcloud config set project YOUR_PROJECT_ID"
else
    ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1)
    PROJECT=$(gcloud config get-value project 2>/dev/null || echo "Not set")
    echo "✅ Authenticated as: $ACCOUNT"
    echo "✅ Current project: $PROJECT"
fi

# Set up terraform.tfvars if it doesn't exist
if [ ! -f terraform.tfvars ]; then
    if [ -f terraform.tfvars.example ]; then
        echo "📝 Creating terraform.tfvars from example..."
        cp terraform.tfvars.example terraform.tfvars
        
        # Try to auto-populate project ID
        PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
        if [ -n "$PROJECT" ]; then
            sed -i "s/your-gcp-project-id/$PROJECT/g" terraform.tfvars
            echo "✅ Set project ID to: $PROJECT"
        else
            echo "⚠️  Please edit terraform.tfvars and set your project_id"
        fi
    else
        echo "❌ terraform.tfvars.example not found"
        exit 1
    fi
else
    echo "✅ terraform.tfvars already exists"
fi

# Initialize Terraform if needed
if [ ! -f .terraform.lock.hcl ]; then
    echo "🔧 Initializing Terraform..."
    if terraform init; then
        echo "✅ Terraform initialized successfully"
    else
        echo "❌ Terraform initialization failed"
        exit 1
    fi
else
    echo "✅ Terraform already initialized"
fi

# Validate Terraform configuration
echo "🔍 Validating Terraform configuration..."
if terraform validate; then
    echo "✅ Terraform configuration is valid"
else
    echo "❌ Terraform configuration validation failed"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Set up helpful aliases in the container
echo "⚙️ Setting up shell aliases..."
cat >> ~/.bashrc << 'EOF'

# GCP VPC Terraform aliases
alias tf='terraform'
alias tfi='terraform init'
alias tfp='terraform plan'
alias tfa='terraform apply'
alias tfd='terraform destroy'
alias tfo='terraform output'
alias tfv='terraform validate'
alias tff='terraform fmt'

# GCP aliases
alias gcl='gcloud'
alias gcla='gcloud auth list'
alias gclp='gcloud config list project'
alias gcls='gcloud config set project'

# Test aliases
alias test-vpc='make test-safe'
alias test-all='make test'

# Utility functions
vpc-status() {
    echo "=== GCP VPC Development Environment ==="
    echo "Project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
    echo "Auth: $(gcloud auth list --filter=status:ACTIVE --format='value(account)' 2>/dev/null || echo 'Not authenticated')"
    echo "Terraform: $(terraform version -json 2>/dev/null | jq -r '.terraform_version' || echo 'Not available')"
    echo "Workspace: $(terraform workspace show 2>/dev/null || echo 'Not initialized')"
    echo ""
    if [ -f terraform.tfvars ]; then
        echo "=== Configuration ==="
        grep -E '^[^#]*=' terraform.tfvars | head -5
    fi
}

EOF

echo ""
echo "🎉 Dev container setup complete!"
echo ""
echo "📋 Summary:"
echo "  ✅ Required tools verified"
echo "  ✅ Python dependencies installed"
echo "  ✅ Terraform configuration validated"
echo "  ✅ Shell aliases configured"
echo ""
echo "🎯 Next steps:"
echo "  1. Run: source ~/.bashrc"
echo "  2. Run: vpc-status"
echo "  3. Run: make plan"
echo ""
echo "📚 Available commands:"
echo "  vpc-status     - Show environment status"
echo "  make setup     - Full setup with auth checks"
echo "  make test-safe - Run tests without creating VMs"
echo "  make plan      - Create Terraform execution plan"
echo "  make apply     - Deploy infrastructure"
echo ""
echo "🔧 Aliases: tf, tfi, tfp, tfa, tfd, tfo, tfv, tff"
echo "🌐 GCP: gcl, gcla, gclp, gcls"
