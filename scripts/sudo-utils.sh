#!/bin/bash
#
# Conventions for sudo-aware scripts
# - Always source this file early in bash scripts:
#     SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
#     PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
#     source "$PROJECT_ROOT/scripts/sudo-utils.sh"
# - Prefer helpers over hand-rolled logic:
#     - install_packages <manager> <pkgs...>
#     - run_with_sudo_if_needed <cmd...>
#     - create_directory <path> [permissions]
# - Behavior:
#     - Root user: never uses sudo
#     - If sudo is required but not present/authorized, emit a clear error and exit
# - Python installs:
#     - Try user-level (--user) first; fall back to system-wide with sudo as needed
# - System paths and service commands are auto-detected as requiring elevation
#


# Sudo utility functions for GCP VPC Terraform scripts
# This script provides functions to check and handle sudo requirements

# Function to check if sudo is required and available for a given command
check_sudo_requirement() {
    # Accept full command with args for detection
    local cmd_str="$*"
    local needs_sudo=false

    # Root never needs sudo
    if [ "$EUID" -eq 0 ]; then
        echo ""
        return
    fi

    # If the command involves known package managers or system services
    if echo "$cmd_str" | grep -Eq '(^|[[:space:]])(apt-get|apt|yum|dnf|pacman|zypper|apk)([[:space:]]|$)'; then
        needs_sudo=true
    elif echo "$cmd_str" | grep -Eq '(^|[[:space:]])(systemctl|service|update-alternatives)([[:space:]]|$)'; then
        needs_sudo=true
    fi

    # If the command references protected system paths anywhere in the args
    if echo "$cmd_str" | grep -Eq '(/etc/|/usr/|/var/|/opt/|/root/|/bin/|/sbin/)'; then
        needs_sudo=true
    fi

    # Special handling for pip installs without --user (system site-packages write)
    if echo "$cmd_str" | grep -Eq '(^|[[:space:]])pip(3)?([[:space:]]|$)'; then
        if echo "$cmd_str" | grep -qE '([[:space:]]|^)install([[:space:]]|$)' && ! echo "$cmd_str" | grep -q -- "--user"; then
            # Check if system purelib is writable; if not, we need sudo
            if ! python3 - <<'PY'
import sysconfig, os
purelib = sysconfig.get_paths().get('purelib')
print('1' if (purelib and os.access(purelib, os.W_OK)) else '0')
PY
            then
                needs_sudo=true
            fi
        fi
    fi

    if [ "$needs_sudo" = true ]; then
        if command -v sudo >/dev/null 2>&1; then
            echo "sudo"
        else
            echo "⚠️  Warning: Command may require elevated privileges, but sudo is not available" >&2
            echo "❌ Please run this script as root or install sudo" >&2
            exit 1
        fi
    else
        echo ""
    fi
}

# Function to run command with sudo if needed
run_with_sudo_if_needed() {
    local sudo_prefix
    local full_cmd="$*"

    sudo_prefix=$(check_sudo_requirement "$full_cmd")

    if [ -n "$sudo_prefix" ];
    then
        echo "🔐 Running with elevated privileges: $full_cmd" >&2
        # Use sudo with proper argument handling
        sudo "$@"
    else
        "$@"
    fi
}

# Function to check if current user has sudo privileges
check_sudo_available() {
    if [ "$EUID" -eq 0 ]; then
        return 0  # Running as root
    fi

    if command -v sudo >/dev/null 2>&1; then
        # Check if user can use sudo without password or with cached credentials
        if sudo -n true 2>/dev/null; then
            return 0  # Can use sudo
        else
            # Try to prompt for sudo password
            echo "🔐 Some operations may require elevated privileges."
            if sudo -v 2>/dev/null; then
                return 0  # Successfully authenticated
            else
                return 1  # Cannot use sudo
            fi
        fi
    else
        return 1  # sudo not available
    fi
}

# Function to install packages with automatic sudo detection
install_packages() {
    local package_manager="$1"
    shift
    local packages="$*"

    case "$package_manager" in
        "apt"|"apt-get")
            run_with_sudo_if_needed apt-get update
            run_with_sudo_if_needed apt-get install -y $packages
            ;;
        "yum")
            run_with_sudo_if_needed yum install -y $packages
            ;;
        "dnf")
            run_with_sudo_if_needed dnf install -y $packages
            ;;
        "pacman")
            run_with_sudo_if_needed pacman -S --noconfirm $packages
            ;;
        "apk")
            run_with_sudo_if_needed apk add $packages
            ;;
        "pip3"|"pip")
            # Try user installation first, fall back to system-wide
            if ! $package_manager install --user $packages 2>/dev/null; then
                echo "⚠️  User-level installation failed, trying system-wide installation..." >&2
                run_with_sudo_if_needed $package_manager install $packages
            fi
            ;;
        *)
            echo "❌ Unsupported package manager: $package_manager" >&2
            return 1
            ;;
    esac
}

# Function to create directories with automatic sudo detection
create_directory() {
    local dir_path="$1"
    local permissions="$2"

    if mkdir -p "$dir_path" 2>/dev/null; then
        if [ -n "$permissions" ]; then
            if ! chmod "$permissions" "$dir_path" 2>/dev/null; then
                run_with_sudo_if_needed chmod "$permissions" "$dir_path"
            fi
        fi
    else
        run_with_sudo_if_needed mkdir -p "$dir_path"
        if [ -n "$permissions" ]; then
            run_with_sudo_if_needed chmod "$permissions" "$dir_path"
        fi
    fi
}

# Function to copy files with automatic sudo detection
copy_file() {
    local source="$1"
    local destination="$2"

    if cp "$source" "$destination" 2>/dev/null; then
        return 0
    else
        run_with_sudo_if_needed cp "$source" "$destination"
    fi
}

# Function to move files with automatic sudo detection
move_file() {
    local source="$1"
    local destination="$2"

    if mv "$source" "$destination" 2>/dev/null; then
        return 0
    else
        run_with_sudo_if_needed mv "$source" "$destination"
    fi
}

# Function to change file ownership with automatic sudo detection
change_ownership() {
    local owner="$1"
    local file_path="$2"

    run_with_sudo_if_needed chown "$owner" "$file_path"
}

# Function to change file permissions with automatic sudo detection
change_permissions() {
    local permissions="$1"
    local file_path="$2"

    if chmod "$permissions" "$file_path" 2>/dev/null; then
        return 0
    else
        run_with_sudo_if_needed chmod "$permissions" "$file_path"
    fi
}

# Function to detect the current operating system and package manager
detect_package_manager() {
    if command -v apt-get >/dev/null 2>&1; then
        echo "apt-get"
    elif command -v yum >/dev/null 2>&1; then
        echo "yum"
    elif command -v dnf >/dev/null 2>&1; then
        echo "dnf"
    elif command -v pacman >/dev/null 2>&1; then
        echo "pacman"
    elif command -v apk >/dev/null 2>&1; then
        echo "apk"
    else
        echo "unknown"
    fi
}

# Function to show sudo status and recommendations
show_sudo_status() {
    echo "=== Sudo Status Check ==="

    if [ "$EUID" -eq 0 ]; then
        echo "✅ Running as root - no sudo required"
        return 0
    fi

    if command -v sudo >/dev/null 2>&1; then
        echo "✅ sudo command is available"

        if sudo -n true 2>/dev/null; then
            echo "✅ sudo privileges are cached/passwordless"
        else
            echo "⚠️  sudo requires password authentication"
            echo "💡 Tip: Run 'sudo -v' to cache credentials"
        fi
    else
        echo "❌ sudo command not found"
        echo "💡 Install sudo or run as root for system operations"
        return 1
    fi

    echo "=========================="
}
