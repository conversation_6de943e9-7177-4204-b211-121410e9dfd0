#!/bin/bash

# Example: Using sudo-aware helpers
# This script demonstrates safe usage of scripts/sudo-utils.sh helpers.

set -euo pipefail

# Locate repo root and source helpers
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
source "$PROJECT_ROOT/scripts/sudo-utils.sh"

# Show sudo status
show_sudo_status || true

# Detect package manager and install a couple of common tools (idempotent)
PKG_MGR=$(detect_package_manager)
echo "Using package manager: $PKG_MGR"
install_packages "$PKG_MGR" jq tree

# Create a local logs directory (no elevation needed)
create_directory "$PROJECT_ROOT/logs"

echo "Listing /etc (read-only) via sudo helper..."
run_with_sudo_if_needed ls -ld /etc

# NOTE: Example of writing to a system path (commented out on purpose):
# echo "example.setting=true" | run_with_sudo_if_needed tee -a /etc/example.conf
# rm -f would also require sudo on system paths; use with caution.

echo "✅ Sudo-aware example completed successfully."
