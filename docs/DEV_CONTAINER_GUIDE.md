# Dev Container Development Guide

This guide explains how to use the dev container setup for GCP VPC Terraform development.

## What is a Dev Container?

A dev container provides a consistent, isolated development environment using Docker. It includes all necessary tools, dependencies, and configurations pre-installed, ensuring that every developer has the same setup regardless of their local machine.

## Benefits

- **Consistency**: Same environment for all developers
- **Isolation**: No conflicts with local tools or dependencies
- **Pre-configured**: All tools and extensions ready to use
- **Portable**: Works on any machine with Docker and VS Code
- **Version Control**: Environment configuration is versioned with code

## Prerequisites

1. **VS Code**: [Download and install](https://code.visualstudio.com/)
2. **Dev Containers Extension**: Install from VS Code marketplace
3. **Docker Desktop**: [Download and install](https://www.docker.com/products/docker-desktop)

## Getting Started

### 1. Open Project in Dev Container

```bash
# Clone the repository
git clone <repository-url>
cd gcp-testing

# Open in VS Code
code .
```

When VS Code opens, you'll see a notification to "Reopen in Container". Click it, or:
- Use Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
- Type "Dev Containers: Reopen in Container"

### 2. First-Time Setup

Once inside the container, run the setup:

```bash
# Authenticate with GCP
gcloud auth login
gcloud auth application-default login

# Set your GCP project
gcloud config set project YOUR_PROJECT_ID

# Run automated setup
make dev-setup
```

### 3. Verify Setup

```bash
# Check environment status
make dev-status

# Or use the built-in function
vpc-status
```

## Development Workflow

### Daily Development

```bash
# Check current status
vpc-status

# Plan infrastructure changes
make plan

# Apply changes
make apply

# Run tests
make test-safe

# Format code
make format
```

### Working with Multiple Projects

```bash
# Switch GCP projects
gcloud config set project NEW_PROJECT_ID

# Update terraform.tfvars
# Edit the project_id variable

# Re-initialize if needed
terraform init
```

## Available Tools and Aliases

### Pre-installed Tools

- **Terraform** (latest version)
- **Google Cloud SDK** (gcloud)
- **Python 3.11** with pip
- **Docker** (for testing)
- **Common utilities**: jq, curl, wget, vim, git

### Shell Aliases

```bash
# Terraform aliases
tf          # terraform
tfi         # terraform init
tfp         # terraform plan
tfa         # terraform apply
tfd         # terraform destroy
tfo         # terraform output
tfv         # terraform validate
tff         # terraform fmt

# GCP aliases
gcl         # gcloud
gcla        # gcloud auth list
gclp        # gcloud config list project
gcls        # gcloud config set project

# Test aliases
test-vpc    # make test-safe
test-all    # make test
```

### Utility Functions

```bash
vpc-status  # Show comprehensive environment status
```

## Customization

### VS Code Extensions

The dev container includes these extensions:
- HashiCorp Terraform
- Python
- Google Cloud Code
- YAML support
- Makefile tools

### Adding More Extensions

Edit `.devcontainer/devcontainer.json`:

```json
{
  "customizations": {
    "vscode": {
      "extensions": [
        "existing.extension",
        "new.extension.id"
      ]
    }
  }
}
```

### Environment Variables

Add environment variables in `.devcontainer/devcontainer.json`:

```json
{
  "containerEnv": {
    "MY_VARIABLE": "value"
  }
}
```

## Troubleshooting

### Container Won't Start

1. **Check Docker**: Ensure Docker Desktop is running
2. **Rebuild Container**: Command Palette → "Dev Containers: Rebuild Container"
3. **Check Logs**: View container logs in VS Code terminal

### Authentication Issues

```bash
# Re-authenticate
gcloud auth login
gcloud auth application-default login

# Check authentication status
gcloud auth list
```

### Terraform Issues

```bash
# Re-initialize Terraform
rm -rf .terraform .terraform.lock.hcl
terraform init

# Validate configuration
terraform validate
```

### Permission Issues

```bash
# Check file permissions
ls -la

# Fix ownership if needed (run as root)
sudo chown -R vscode:vscode /workspace
```

## Performance Tips

### Volume Mounts

The dev container mounts:
- Project files: `/workspace`
- GCP credentials: `~/.config/gcloud`
- Terraform cache: `~/.terraform.d`

### Speeding Up Rebuilds

1. **Use .dockerignore**: Exclude unnecessary files
2. **Layer Caching**: Order Dockerfile commands by change frequency
3. **Persistent Volumes**: Cache dependencies between rebuilds

## Security Considerations

### Credential Management

- GCP credentials are mounted read-only from host
- Never commit credentials to version control
- Use application default credentials when possible

### Network Security

- Container runs in isolated network
- Only necessary ports are forwarded
- No direct access to host network

## Advanced Usage

### Multiple Environments

```bash
# Use Terraform workspaces
terraform workspace new development
terraform workspace new staging
terraform workspace new production

# Switch between workspaces
terraform workspace select development
```

### Custom Scripts

Add custom scripts to `/workspace/scripts/`:

```bash
# Make executable
chmod +x scripts/my-script.sh

# Add to PATH (in .bashrc)
export PATH="/workspace/scripts:$PATH"
```

### Debugging

```bash
# Enable Terraform debug logging
export TF_LOG=DEBUG
export TF_LOG_PATH=/workspace/logs/terraform-debug.log

# Run with logging
terraform plan
```

## Integration with CI/CD

The dev container configuration can be used in CI/CD pipelines:

```yaml
# GitHub Actions example
- name: Setup Dev Container
  uses: devcontainers/ci@v0.3
  with:
    imageName: ghcr.io/your-org/gcp-vpc-terraform
```

## Best Practices

1. **Regular Updates**: Rebuild container periodically for updates
2. **Clean State**: Use `terraform workspace` for environment isolation
3. **Version Control**: Keep dev container config in git
4. **Documentation**: Update this guide when making changes
5. **Testing**: Test dev container setup on different machines

## Support

For issues with the dev container setup:

1. Check this documentation
2. Review `.devcontainer/` configuration files
3. Check Docker and VS Code logs
4. Rebuild the container
5. Create an issue in the project repository
