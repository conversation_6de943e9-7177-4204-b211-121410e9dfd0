# Firewall Module
# Creates custom and default firewall rules

# Create custom firewall rules
resource "google_compute_firewall" "firewall_rules" {
  for_each = var.firewall_rules

  name    = each.key
  network = var.vpc_network.name

  allow {
    protocol = each.value.protocol
    ports    = each.value.ports
  }

  source_ranges = each.value.source_ranges
  target_tags   = each.value.target_tags
  description   = each.value.description
}

# Default firewall rules
resource "google_compute_firewall" "allow_internal" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-internal"
  network = var.vpc_network.name

  allow {
    protocol = "icmp"
  }

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  source_ranges = [for subnet in var.subnets : subnet.cidr]
  description   = "Allow internal communication within VPC"
}

resource "google_compute_firewall" "allow_ssh" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-ssh"
  network = var.vpc_network.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = var.ssh_source_ranges
  target_tags   = ["ssh-allowed"]
  description   = "Allow SSH access"
}

resource "google_compute_firewall" "allow_http_https" {
  count = var.create_default_firewall_rules ? 1 : 0

  name    = "${var.vpc_name}-allow-http-https"
  network = var.vpc_network.name

  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["http-server", "https-server"]
  description   = "Allow HTTP and HTTPS traffic"
}
