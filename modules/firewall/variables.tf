# Variables for Firewall Module

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "vpc_network" {
  description = "The VPC network resource"
  type = object({
    id        = string
    name      = string
    self_link = string
  })
}

variable "firewall_rules" {
  description = "Map of custom firewall rules"
  type = map(object({
    protocol      = string
    ports         = list(string)
    source_ranges = list(string)
    target_tags   = list(string)
    description   = string
  }))
  default = {}
}

variable "create_default_firewall_rules" {
  description = "Whether to create default firewall rules"
  type        = bool
  default     = true
}

variable "ssh_source_ranges" {
  description = "Source IP ranges allowed for SSH access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "subnets" {
  description = "Map of subnets for internal firewall rules"
  type = map(object({
    cidr                  = string
    region                = string
    description           = string
    private_google_access = bool
    enable_flow_logs      = bool
    secondary_ranges = list(object({
      range_name    = string
      ip_cidr_range = string
    }))
  }))
}
