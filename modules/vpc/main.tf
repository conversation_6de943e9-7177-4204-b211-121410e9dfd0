# VPC Network Module
# Creates the main VPC network with configurable settings

# Create VPC Network
resource "google_compute_network" "vpc_network" {
  name                    = var.vpc_name
  auto_create_subnetworks = false
  mtu                     = var.vpc_mtu
  description             = "VPC network created by Terraform"

  # Enable deletion protection for production environments
  delete_default_routes_on_create = var.delete_default_routes
}
