# Variables for Subnets Module

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "vpc_network" {
  description = "The VPC network resource"
  type = object({
    id        = string
    name      = string
    self_link = string
  })
}

variable "subnets" {
  description = "Map of subnets to create"
  type = map(object({
    cidr                  = string
    region                = string
    description           = string
    private_google_access = bool
    enable_flow_logs      = bool
    secondary_ranges = list(object({
      range_name    = string
      ip_cidr_range = string
    }))
  }))
}
