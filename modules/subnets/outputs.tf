# Outputs for Subnets Module

output "subnets" {
  description = "Map of subnet resources"
  value       = google_compute_subnetwork.subnets
}

output "subnet_ids" {
  description = "Map of subnet names to their IDs"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.id
  }
}

output "subnet_self_links" {
  description = "Map of subnet names to their self-links"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.self_link
  }
}

output "subnet_cidr_ranges" {
  description = "Map of subnet names to their CIDR ranges"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.ip_cidr_range
  }
}

output "subnet_gateway_addresses" {
  description = "Map of subnet names to their gateway addresses"
  value = {
    for name, subnet in google_compute_subnetwork.subnets : name => subnet.gateway_address
  }
}

output "subnet_regions" {
  description = "List of distinct regions used by subnets"
  value       = local.subnet_regions
}
