# Subnets Module
# Creates subnets with configurable settings across multiple regions

# Derive distinct regions from provided subnets for multi-region support
locals {
  subnet_regions = distinct([for s in values(var.subnets) : s.region])
}

# Create subnets
resource "google_compute_subnetwork" "subnets" {
  for_each = var.subnets

  name          = each.key
  ip_cidr_range = each.value.cidr
  region        = each.value.region
  network       = var.vpc_network.id
  description   = each.value.description

  # Enable private Google access if specified
  private_ip_google_access = each.value.private_google_access

  # Secondary IP ranges for GKE clusters if specified
  dynamic "secondary_ip_range" {
    for_each = each.value.secondary_ranges
    content {
      range_name    = secondary_ip_range.value.range_name
      ip_cidr_range = secondary_ip_range.value.ip_cidr_range
    }
  }

  # Enable flow logs if specified
  dynamic "log_config" {
    for_each = each.value.enable_flow_logs ? [1] : []
    content {
      aggregation_interval = "INTERVAL_10_MIN"
      flow_sampling        = 0.5
      metadata             = "INCLUDE_ALL_METADATA"
    }
  }
}
