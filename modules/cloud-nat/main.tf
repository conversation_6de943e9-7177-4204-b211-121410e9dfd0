# Cloud NAT Module
# Creates Cloud Routers and NAT gateways per region

# Create Cloud Routers per region for NAT Gateway
resource "google_compute_router" "router" {
  for_each = var.enable_nat ? toset(var.subnet_regions) : toset([])

  name    = "${var.vpc_name}-router"
  region  = each.key
  network = var.vpc_network.id

  bgp {
    asn = var.router_asn
  }
}

# Create Cloud NAT per region
resource "google_compute_router_nat" "nat" {
  for_each = var.enable_nat ? toset(var.subnet_regions) : toset([])

  name                               = "${var.vpc_name}-nat"
  router                             = google_compute_router.router[each.key].name
  region                             = each.key
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}
