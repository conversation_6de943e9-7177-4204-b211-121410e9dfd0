# Outputs for Cloud NAT Module

output "routers" {
  description = "Map of router resources"
  value       = google_compute_router.router
}

output "router_ids" {
  description = "Map of router names to their IDs"
  value = {
    for name, router in google_compute_router.router : name => router.id
  }
}

output "nat_gateways" {
  description = "Map of NAT gateway resources"
  value       = google_compute_router_nat.nat
}

output "nat_gateway_ids" {
  description = "Map of NAT gateway names to their IDs"
  value = {
    for name, nat in google_compute_router_nat.nat : name => nat.id
  }
}
