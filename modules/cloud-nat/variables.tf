# Variables for Cloud NAT Module

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "vpc_network" {
  description = "The VPC network resource"
  type = object({
    id        = string
    name      = string
    self_link = string
  })
}

variable "enable_nat" {
  description = "Whether to enable Cloud NAT for private subnets"
  type        = bool
  default     = true
}

variable "router_asn" {
  description = "ASN for the Cloud Router"
  type        = number
  default     = 64514
}

variable "subnet_regions" {
  description = "List of regions where subnets are deployed"
  type        = list(string)
}
