# Docker Compose for GCP VPC Terraform development
# Alternative to dev containers for local development

version: '3.8'

services:
  terraform-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: gcp-vpc-terraform-dev
    volumes:
      - .:/workspace
      - ~/.config/gcloud:/home/<USER>/.config/gcloud:ro
      - terraform-cache:/home/<USER>/.terraform.d
      - python-cache:/home/<USER>/.cache/pip
    working_dir: /workspace
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.config/gcloud/application_default_credentials.json
      - TF_LOG_PATH=/workspace/logs/terraform.log
    ports:
      - "8080:8080"
      - "8081:8081"
      - "8082:8082"
    stdin_open: true
    tty: true
    command: /bin/bash
    networks:
      - terraform-net

  # Optional: Terraform state backend (for team development)
  terraform-backend:
    image: hashicorp/terraform:latest
    container_name: terraform-backend
    volumes:
      - terraform-state:/terraform-state
    environment:
      - TF_DATA_DIR=/terraform-state
    networks:
      - terraform-net
    profiles:
      - backend

volumes:
  terraform-cache:
    driver: local
  python-cache:
    driver: local
  terraform-state:
    driver: local

networks:
  terraform-net:
    driver: bridge
