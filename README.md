# GCP VPC Terraform Configuration

This Terraform configuration automates the creation of a Virtual Private Cloud (VPC) network in Google Cloud Platform with subnets, firewall rules, NAT gateway, and other networking components.

## Features

- **VPC Network**: Creates a custom VPC with configurable MTU
- **Subnets**: Multiple subnets with customizable CIDR ranges, regions, and settings
- **Cloud NAT**: Optional NAT gateway for private subnet internet access
- **Firewall Rules**: Default and custom firewall rules for security
- **Flow Logs**: Optional VPC flow logs for network monitoring
- **Secondary IP Ranges**: Support for GKE pod and service IP ranges
- **Private Google Access**: Enable private access to Google services

## Development Environment Options

### Option 1: Dev Container (Recommended)
Use VS Code with the Dev Containers extension for a consistent, isolated environment:

1. **Prerequisites**:
   - [VS Code](https://code.visualstudio.com/)
   - [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
   - [Docker Desktop](https://www.docker.com/products/docker-desktop)

2. **Setup**:
   ```bash
   # Open in VS Code
   code .

   # VS Code will prompt to "Reopen in Container"
   # Or use Command Palette: "Dev Containers: Reopen in Container"
   ```

3. **First-time setup in container**:
   ```bash
   # Authenticate with GCP
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project YOUR_PROJECT_ID

   # Run setup
   make dev-setup
   ```

### Option 2: Docker Compose
For local development without VS Code:

```bash
# Build and start the development container
docker-compose up -d terraform-dev

# Enter the container
docker-compose exec terraform-dev bash

# Run setup
make dev-setup
```

### Option 3: Local Installation
Install tools directly on your machine:

1. **Prerequisites**:
   - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
   - [Terraform](https://www.terraform.io/downloads.html) (>= 1.0)
   - Python 3.9+ with pip
   - GCP Project with billing enabled

2. **Authentication**:
   - Service Account Key: `export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"`
   - Application Default Credentials: `gcloud auth application-default login`

## Required GCP APIs

Enable the following APIs in your GCP project:

```bash
gcloud services enable compute.googleapis.com
gcloud services enable servicenetworking.googleapis.com
gcloud services enable dns.googleapis.com
```

## Quick Start

### Using Dev Container (Recommended)

1. **Open in VS Code**:
   ```bash
   code .
   # Select "Reopen in Container" when prompted
   ```

2. **Setup in Container**:
   ```bash
   # Authenticate with GCP
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project YOUR_PROJECT_ID

   # Run automated setup
   make dev-setup
   ```

3. **Deploy Infrastructure**:
   ```bash
   make plan    # Review the execution plan
   make apply   # Deploy the VPC infrastructure
   ```

### Using Local Environment

1. **Clone and Configure**:
   ```bash
   # Copy the example variables file
   cp terraform.tfvars.example terraform.tfvars

   # Edit terraform.tfvars with your project ID and desired configuration
   nano terraform.tfvars
   ```

2. **Initialize and Deploy**:
   ```bash
   make setup   # Initialize Terraform and check prerequisites
   make plan    # Create execution plan
   make apply   # Deploy infrastructure
   ```

## Configuration

### Required Variables

- `project_id`: Your GCP project ID

### Optional Variables

- `region`: GCP region (default: "us-central1")
- `vpc_name`: Name of the VPC network (default: "main-vpc")
- `subnets`: Map of subnets to create with their configurations
- `enable_nat`: Whether to create a Cloud NAT gateway (default: true)
- `firewall_rules`: Custom firewall rules to create
- `create_default_firewall_rules`: Whether to create default firewall rules (default: true)

### Example Subnet Configuration

```hcl
subnets = {
  "web-subnet" = {
    cidr                  = "********/24"
    region               = "us-central1"
    description          = "Public subnet for web servers"
    private_google_access = false
    enable_flow_logs     = true
    secondary_ranges     = []
  }
  "gke-subnet" = {
    cidr                  = "********/24"
    region               = "us-central1"
    description          = "Subnet for GKE cluster"
    private_google_access = true
    enable_flow_logs     = true
    secondary_ranges = [
      {
        range_name    = "gke-pods"
        ip_cidr_range = "********/16"
      },
      {
        range_name    = "gke-services"
        ip_cidr_range = "********/16"
      }
    ]
  }
}
```

## Outputs

The configuration provides several outputs that can be used by other Terraform modules:

- `vpc_network_id`: The ID of the created VPC network
- `subnet_ids`: Map of subnet names to their IDs
- `subnet_cidr_ranges`: Map of subnet names to their CIDR ranges
- `network_info`: Complete network information for use by other modules

## Security Considerations

1. **Firewall Rules**: Review and customize firewall rules for your security requirements
2. **SSH Access**: Restrict SSH source ranges in production environments
3. **Private Subnets**: Use private subnets for application and database tiers
4. **Flow Logs**: Enable flow logs for security monitoring and troubleshooting

## Best Practices

1. **Remote State**: Configure remote state storage using GCS backend
2. **Least Privilege**: Use minimal required firewall rules
3. **Network Segmentation**: Separate different tiers (web, app, database) into different subnets
4. **Monitoring**: Enable flow logs and set up monitoring alerts
5. **Tagging**: Use consistent tagging for resource management


## Sudo-aware scripting utilities

Some setup scripts automatically detect when a command needs elevated privileges and will prepend sudo when required.

- Utility module: scripts/sudo-utils.sh
- Works both inside and outside dev containers
- If sudo is unavailable and elevation is required, the script will exit with a clear message

Available helper functions (bash):
- show_sudo_status — prints current sudo capability status
- detect_package_manager — detects apt-get/yum/dnf/pacman/apk
- install_packages <manager> <pkgs...> — installs packages with sudo if needed
- run_with_sudo_if_needed <cmd...> — runs any command with sudo when required

Using in your own scripts:

```bash
# In your script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
source "$PROJECT_ROOT/scripts/sudo-utils.sh"

show_sudo_status
install_packages "$(detect_package_manager)" curl jq
run_with_sudo_if_needed tee -a /etc/example.conf <<<"config=value"
```

Notes:
- Python dependencies attempt a user-level install first; if that fails, scripts try a system-wide install with sudo
- System path touches (e.g., /etc, /usr) are auto-detected as requiring elevation

## Cleanup

To destroy the created resources:

```bash
terraform destroy
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure proper GCP authentication is configured
2. **API Not Enabled**: Enable required GCP APIs in your project
3. **Quota Limits**: Check GCP quotas for your project
4. **CIDR Conflicts**: Ensure subnet CIDR ranges don't overlap

### Useful Commands

```bash
# Validate configuration
terraform validate

# Format configuration files
terraform fmt

# Show current state
terraform show

# List resources
terraform state list
```

## Contributing

1. Follow Terraform best practices
2. Update documentation for any changes
3. Test configurations before submitting
4. Use meaningful commit messages

## License

This configuration is provided as-is for educational and operational purposes.
