# Variables for GCP VPC Terraform configuration

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "The GCP zone"
  type        = string
  default     = "us-central1-a"
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
  default     = "main-vpc"
}

variable "vpc_mtu" {
  description = "Maximum Transmission Unit in bytes"
  type        = number
  default     = 1460
}

variable "delete_default_routes" {
  description = "Whether to delete default routes on VPC creation"
  type        = bool
  default     = false
}

variable "subnets" {
  description = "Map of subnets to create"
  type = map(object({
    cidr                   = string
    region                 = string
    description            = string
    private_google_access  = bool
    enable_flow_logs      = bool
    secondary_ranges = list(object({
      range_name    = string
      ip_cidr_range = string
    }))
  }))
  default = {
    "public-subnet" = {
      cidr                  = "********/24"
      region               = "us-central1"
      description          = "Public subnet for web servers"
      private_google_access = false
      enable_flow_logs     = true
      secondary_ranges     = []
    }
    "private-subnet" = {
      cidr                  = "********/24"
      region               = "us-central1"
      description          = "Private subnet for application servers"
      private_google_access = true
      enable_flow_logs     = true
      secondary_ranges     = []
    }
    "database-subnet" = {
      cidr                  = "********/24"
      region               = "us-central1"
      description          = "Private subnet for databases"
      private_google_access = true
      enable_flow_logs     = true
      secondary_ranges     = []
    }
  }
}

variable "enable_nat" {
  description = "Whether to enable Cloud NAT for private subnets"
  type        = bool
  default     = true
}

variable "router_asn" {
  description = "ASN for the Cloud Router"
  type        = number
  default     = 64514
}

variable "firewall_rules" {
  description = "Map of custom firewall rules"
  type = map(object({
    protocol      = string
    ports         = list(string)
    source_ranges = list(string)
    target_tags   = list(string)
    description   = string
  }))
  default = {}
}

variable "create_default_firewall_rules" {
  description = "Whether to create default firewall rules"
  type        = bool
  default     = true
}

variable "ssh_source_ranges" {
  description = "Source IP ranges allowed for SSH access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# Optional variables for advanced configurations

variable "enable_private_service_connect" {
  description = "Whether to enable Private Service Connect"
  type        = bool
  default     = false
}

variable "dns_policy" {
  description = "DNS policy configuration"
  type = object({
    enable_inbound_forwarding  = bool
    enable_logging            = bool
    networks                  = list(string)
  })
  default = {
    enable_inbound_forwarding = false
    enable_logging           = false
    networks                 = []
  }
}

variable "peering_connections" {
  description = "VPC peering connections to create"
  type = map(object({
    peer_network = string
    auto_create_routes = bool
  }))
  default = {}
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
